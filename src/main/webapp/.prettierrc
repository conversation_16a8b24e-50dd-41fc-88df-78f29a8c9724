{"printWidth": 140, "singleQuote": true, "tabWidth": 2, "useTabs": false, "arrowParens": "avoid", "plugins": ["prettier-plugin-package<PERSON><PERSON>", "@trivago/prettier-plugin-sort-imports"], "importOrder": ["^(vue|@vue/.*|@vuelidate/.*|vue-i18n|vue-router|flag-icons/.*|pinia|quasar|quasar/.*|@quasar/.*|boot/.*)$", "^(components|pages|src|/src)/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}