{"global": {"title": "Afakto", "browsehappy": "Vous utilisez un <strong>ancien</strong> navigateur. Veuillez <a href=\"http://browsehappy.com/?locale=fr\">mettre à jour votre navigateur</a> pour une meilleure expérience.", "menu": {"home": "Accueil", "jhipster-needle-menu-add-element": "JHipster will add additional menu entries here (do not translate!)", "entities": {"dashboard": "Tableau de bord", "main": "Entités", "buyer": "Acheteurs", "country": "Pays", "bankTransaction": "Transactions", "address": "<PERSON><PERSON><PERSON>", "contact": "Contact", "contract": "Contrats", "company": "Entreprises", "factorInstitution": "Factors", "datastream": "Flux de données", "paymentTerms": "<PERSON><PERSON><PERSON><PERSON>", "invoice": "Factures", "cession": "Cessions", "creditLimit": "Limites de Crédit", "creditLimitHistory": "Historique de Limite de Crédit", "creditLimitRequest": "Limites de Crédit", "externalCreditInsurance": "Assureurs", "user": "Utilisateurs", "jhipster-needle-menu-add-entry": "JHipster will add additional entities here (do not translate!)"}, "account": {"main": "<PERSON><PERSON><PERSON>", "settings": "Profil", "password": "Mot de passe", "sessions": "Sessions", "login": "S'authentifier", "logout": "Déconnexion", "register": "<PERSON><PERSON><PERSON> un compte"}, "admin": {"main": "Administration", "userManagement": "Gestion des utilisateurs", "tracker": "Suivi des utilisateurs", "metrics": "Métriques", "health": "Diagnostiques", "configuration": "Configuration", "logs": "Logs", "apidocs": "API", "database": "Base de données", "jhipster-needle-menu-add-admin-element": "JHipster will add additional menu entries here (do not translate!)"}, "language": "<PERSON><PERSON>", "help": "Aide"}, "form": {"username.label": "Nom d'utilisateur", "username.placeholder": "Votre nom d'utilisateur", "currentpassword.label": "Mot de passe actuel", "currentpassword.placeholder": "Mot de passe actuel", "newpassword.label": "Nouveau mot de passe", "newpassword.placeholder": "Nouveau mot de passe", "confirmpassword.label": "Confirmation du nouveau mot de passe", "confirmpassword.placeholder": "Confirmation du nouveau mot de passe", "email.label": "Email", "email.placeholder": "Votre email", "beforeUnload": "Êtes-vous sûr de vouloir quitter ce formulaire?", "empty": "Sans valeur"}, "messages": {"info": {"authenticated": {"prefix": "Si vous voulez vous ", "link": "connecter", "suffix": ", vous pouvez utiliser les comptes par défaut: <br/> - Administrateur (nom d'utilisateur=\"admin\" et mot de passe =\"admin\") <br/> - Utilisateur (nom d'utilisateur=\"user\" et mot de passe =\"user\")."}, "goodbye": {"loggedOut": "Vous avez été déconnecté", "close": "<PERSON>ous pouvez fermer cette fenêtre", "signIn": "Cliquez ici pour vous reconnecter"}, "register": {"noaccount": "Vous n'avez pas encore de compte?", "link": "<PERSON><PERSON><PERSON> un compte"}}, "error": {"dontmatch": "Le nouveau mot de passe et sa confirmation ne sont pas égaux !"}, "validate": {"newpassword": {"required": "Votre mot de passe est requis.", "minlength": "Votre mot de passe doit comporter au moins 4 caractères.", "maxlength": "Votre mot de passe ne doit pas comporter plus de 50 caractères.", "strength": "Robustesse du mot de passe :"}, "confirmpassword": {"required": "Votre confirmation du mot de passe est requise.", "minlength": "Votre confirmation du mot de passe doit comporter au moins 4 caractères.", "maxlength": "Votre confirmation du mot de passe ne doit pas comporter plus de 50 caractères."}, "email": {"required": "Votre email est requis.", "invalid": "Votre email n'est pas valide.", "minlength": "Votre email doit comporter au moins 5 caractères.", "maxlength": "Votre email ne doit pas comporter plus de 50 caractères."}}}, "field": {"id": "ID", "createdDate": "<PERSON><PERSON><PERSON>", "createdBy": "C<PERSON><PERSON> par", "lastModifiedDate": "Mis à jour", "lastModifiedBy": "Mis à jour par", "by": "par", "warnings": "<PERSON><PERSON><PERSON>"}, "ribbon": {"dev": "Développement"}, "item-count": "Affichage {first} - {second} de {total} items."}, "entity": {"action": {"add": "Ajouter", "addblob": "Ajouter blob", "addimage": "Ajouter image", "addfilter": "Ajouter un filtre", "back": "Retour", "cancel": "Annuler", "close": "<PERSON><PERSON><PERSON>", "columns": "Colonnes", "configure": "Configurer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Editer", "export": "Télécharger", "filters": "Filtres", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "save": "<PERSON><PERSON><PERSON><PERSON>", "submit": "Envoyer", "request": "<PERSON><PERSON><PERSON>", "view": "Voir", "import": "Import", "update": "Mise à jour"}, "detail": {"field": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "days": "Jours"}, "delete": {"title": "Confirmation de suppression"}, "validation": {"required": "Ce champ est obligatoire.", "minlength": "Ce champ doit faire au minimum {min} caractères.", "maxlength": "Ce champ doit faire moins de {max} caractères.", "min": "Ce champ doit être supérieur à {min}.", "max": "Ce champ doit être inférieur à {max}.", "minbytes": "Ce champ doit être supérieur à {min} bytes.", "maxbytes": "Ce champ doit être inférieur à {max} bytes.", "pattern": "Ce champ doit suivre l'expression régulière {pattern}.", "number": "Ce champ doit être un nombre.", "datetimelocal": "Ce champ doit être une date et une heure.", "patternLogin": "Ce champ ne peut contenir que des lettres, des chiffres ou des adresses e-mail."}}, "error": {"dataIntegrityViolation": "Violation de l'intégrité des données", "internalServerError": "<PERSON>rreur interne du serveur", "server.not.reachable": "Serveur inaccessible", "url.not.found": "Non trouvé", "NotNull": "Le champ {fieldName} ne peut pas être vide!", "Size": "Le champ {fieldName} ne respecte pas les critères minimum et maximum!", "userexists": "Login déjà utilisé!", "emailexists": "Email déjà utilisé!", "idexists": "Une nouvelle entité {entityName} ne peut pas avoir d'identifiant!", "idnull": "Identifiant invalide", "idinvalid": "ID Invalide", "idnotfound": "ID ne peut être trouvé", "file": {"could.not.extract": "Impossible d'extraire le fichier", "not.image": "Le fichier doit être une image et non du type \"{ fileType }\""}}, "footer": "Ceci est votre pied de page"}