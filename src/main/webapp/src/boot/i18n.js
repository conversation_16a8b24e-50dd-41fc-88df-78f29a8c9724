import { boot } from 'quasar/wrappers';
import { createI18n } from 'vue-i18n';

import { datefnsMapping } from 'src/constants/i18nConstants';

const commonFormat = {
  currency: {
    minimumFractionDigits: 2,
    useGrouping: true,
  },
  compact: {
    style: 'currency',
    notation: 'compact',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    signDisplay: 'exceptZero',
    useGrouping: true,
  },
  currencyCode: {
    style: 'currency',
    currencyDisplay: 'code',
    minimumFractionDigits: 2,
    useGrouping: true,
  },
  percent: {
    maximumFractionDigits: 2,
    signDisplay: 'negative',
  },
};

const datetimeFormats = {
  en: {
    short: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    },
    long: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    },
  },
  fr: {
    short: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    },
    long: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    },
  },
};

const numberFormats = {
  en: { ...commonFormat },
  fr: { ...commonFormat },
};

export const i18n = createI18n({
  datetimeFormats,
  fallbackLocale: 'en',
  numberFormats,
  legacy: false,
});

export default boot(({ app }) => app.use(i18n));

export const loadTranslation = async langKey => {
  const messages = (await import(`../../i18n/${langKey}/${langKey}.js`)).default;
  const mapping = datefnsMapping[langKey];
  window.__localeId__ = mapping || langKey;

  if ('value' in i18n.global.locale) i18n.global.locale.value = langKey;
  i18n.global.setLocaleMessage(langKey, messages);
};
