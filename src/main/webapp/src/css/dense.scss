@use 'sass:color'; // Import Sass color functions

@import 'quasar/src/components/checkbox/QCheckbox', 'quasar/src/components/chip/QChip', 'quasar/src/components/field/QField',
  'quasar/src/components/item/QItem', 'quasar/src/components/radio/QRadio', 'quasar/src/components/table/QTable',
  'quasar/src/components/toggle/QToggle';

.q-table__card {
  box-shadow: none;
}

.q-table__card--dark {
  color: white;
}

.q-page-container.dense {
  h1 {
    font-size: 1.6rem;
  }

  // .q-page {

  // figma faithful tab design
  // padding: 32px;

  // background-color: $container;

  // .q-toolbar {
  //   background-color: $surface;
  //   padding: 4px 6px;

  //     &:nth-of-type(1) {
  //       border-radius: 10px;
  //       margin-bottom: 16px;
  //     }

  //     &:nth-of-type(2) {
  //       padding-bottom: 12px;
  //       margin-top: -16px;
  //     }
  //   }

  // }

  // q.table, tbody {
  //       background-color: $surface;
  //   }

  .q-table__card {
    box-shadow: none !important;
  }

  .q-table__container {
    @extend .q-table--dense;
    .q-avatar {
      font-size: 16px !important;
    }
    .q-td > .q-icon:first-child {
      font-size: 16px !important;
    }
    .q-btn {
      margin-bottom: -6px !important;
      margin-top: -6px !important;
    }
  }

  .q-checkbox {
    @extend .q-checkbox--dense;
  }
  .q-chip {
    @extend .q-chip--dense;
  }
  .q-item {
    @extend .q-item--dense;
  }
  .q-radio {
    @extend .q-radio--dense;
  }
  .q-toggle {
    @extend .q-toggle--dense;
  }
}
