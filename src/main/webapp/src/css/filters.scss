// Centralized filter styles
// Used across multiple components for consistent filtering UI

// Base filter styles
%filter-base {
  border-radius: 9999px !important;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  min-width: 12em;
  max-width: 12em;
  font-size: 12px !important;

  .q-field__control {
    height: 36px !important;
    min-height: 30px !important;
    padding-left: 8px !important;
    padding-right: 8px !important;
    transform: none !important;
    position: relative;
    top: 0;
  }

  .q-field__inner {
    align-items: center;
  }
}

// Default filter state
.filter {
  @extend %filter-base;
  background-color: $backgroundTertiary;
  border: 1px solid $neutralHigh;

  .body--dark & {
    background-color: $neutralHigher;
    border-color: $neutralHigh;
  }
}

// Active filter state
.filter--active {
  @extend %filter-base;
  background-color: $infoLowest !important;
  border: 1px solid $infoMedium !important;

  .q-field__label {
    color: $infoMedium !important;
    font-weight: bold;
  }

  .q-field__native,
  .q-icon {
    color: $infoMedium !important;
  }

  .body--dark & {
    background-color: $infoLowestDark !important;
  }
}

// Add filter button
.filter-add {
  box-shadow: none !important;

  .q-field__marginal {
    height: 100%;
  }

  .q-field__control,
  .q-field__native {
    height: 26px !important;
    min-height: 26px !important;
    padding: 0;
  }

  .q-field__native {
    color: $neutralHigh !important;
    font-weight: 500;
  }
}

// Reset filter button
.filter-reset {
  color: $neutralHigh;
  padding: 0;
}
