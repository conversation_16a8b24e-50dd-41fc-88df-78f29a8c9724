<template>
  <q-page class="q-pa-md flex flex-center">
    <b-title :model-value="$t('afaktoApp.creditLimit.detail.title')" />
    <q-form class="responsive-width q-gutter-md" greedy @submit="onSubmit">
      <q-input
        v-model="creditLimit.data.totalOutstanding"
        :label="$t('afaktoApp.creditLimit.totalOutstanding')"
        :rules="[$rules.decimal()]"
      />
      <q-input
        v-model="creditLimit.data.outstandingUnderFactor"
        :label="$t('afaktoApp.creditLimit.outstandingUnderFactor')"
        :rules="[$rules.decimal()]"
      />
      <q-input
        v-model="creditLimit.data.currentCreditLimit"
        :label="$t('afaktoApp.creditLimit.currentCreditLimit')"
        :rules="[$rules.decimal()]"
      />
      <q-input
        v-model="creditLimit.data.requestedCreditLimit"
        :label="$t('afaktoApp.creditLimit.requestedCreditLimit')"
        :rules="[$rules.decimal()]"
      />
      <q-input v-model="creditLimit.data.date" :label="$t('afaktoApp.creditLimit.date')" />
      <q-select v-model="creditLimit.data.status" :label="$t('afaktoApp.creditLimit.status')" :options="[null, 'OK', 'REQUESTED']" />
      <div class="flex justify-between">
        <q-btn type="submit" color="primary" :label="$t('entity.action.save')" :loading="loading" :disable="loading" />
      </div>
    </q-form>
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { useQuasar } from 'quasar';
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

const baseApiUrl = '/api/credit-limits';
const $q = useQuasar();
const router = useRouter();
const route = useRoute();
const { t } = useI18n();

const creditLimit = reactive({
  data: {
    id: null,
    totalOutstanding: null,
    outstandingUnderFactor: null,
    currentCreditLimit: null,
    requestedCreditLimit: null,
    date: null,
    status: null,
  },
});

const loading = ref(false);

(async function fetchCreditLimit() {
  if (route.params.id) {
    creditLimit.data = (await api.get(`${baseApiUrl}/${route.params.id}`)).data;
  }
})();

const onSubmit = async () => {
  loading.value = true;
  try {
    await api({
      method: creditLimit.data.id ? 'put' : 'post',
      baseURL: baseApiUrl,
      url: creditLimit.data.id,
      data: creditLimit.data,
    });
    router.back();
  } catch (e) {
    loading.value = false;
    if (e.response.status !== 400) return;
    $q.notify({
      type: 'negative',
      message: t(e.response.data.message),
    });
  }
};
</script>
