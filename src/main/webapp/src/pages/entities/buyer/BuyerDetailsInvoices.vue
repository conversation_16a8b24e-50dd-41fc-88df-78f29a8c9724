<template>
  <q-toolbar>
    <q-space />

    <rows-export
      :base-api-url="baseApiUrl"
      :columns="columns"
      :filters="{ buyer: { operation: 'equals', value: route.params.id }, ...filters }"
      :pagination="pagination"
      :visible-columns="visibleColumns"
    />
  </q-toolbar>

  <q-toolbar>
    <columns-filtering v-model="filters" :columns="columns" />
    <columns-visibility v-model="visibleColumns" :columns="columns" />
  </q-toolbar>

  <q-table
    v-model:pagination="pagination"
    binary-state-sort
    :columns="columns"
    row-key="id"
    :rows="rows"
    :rows-per-page-options="[0]"
    :visible-columns="visibleColumns"
    @request="onRequest"
    @row-click="(_event, { id }) => router.push(`/invoices/${id}`)"
  >
    <template #header-cell-gap>
      <q-th class="boolean" />
    </template>
    <template #body-cell-isUnderFactor="props">
      <q-td :props="props">
        <q-icon v-if="!!props.value" color="blue" name="task_alt" size="md" />
      </q-td>
    </template>
    <template #body-cell-gap="props">
      <q-td class="boolean">
        <q-icon v-if="props.value" color="warning" name="bolt" size="md"> </q-icon>
        <q-tooltip v-if="props.value">
          {{ $t(`afaktoApp.invoice.gapType.${props.value}`) }}
        </q-tooltip>
      </q-td>
    </template>
  </q-table>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { InvoiceType } from 'src/shared/model/enumerations/invoice-type.model';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { filtersToQueryParams } from 'src/util/filtering';
import { format } from 'src/util/format';

const baseApiUrl = '/api/invoices';
const { n, t } = useI18n();
const route = useRoute();
const router = useRouter();
const store = useAuthenticationStore();

const columns = computed(() => [
  {
    classes: 'type',
    field: row => t(`afaktoApp.InvoiceType.${row.type}`),
    filter: {
      type: 'enum',
      values: Object.values(InvoiceType).map(type => ({
        label: t(`afaktoApp.InvoiceType.${type}`),
        value: type,
      })),
    },
    hidden: true,
    headerClasses: 'type',
    label: t('afaktoApp.invoice.type'),
    name: 'type',
    sortable: true,
  },
  {
    align: 'left',
    field: 'invoiceNumber',
    filter: { type: 'text' },
    label: t('afaktoApp.invoice.invoiceNumber'),
    name: 'invoiceNumber',
    sortable: true,
  },
  {
    field: 'date',
    filter: { type: 'date' },
    format: value => value && format(value),
    label: t('afaktoApp.invoice.date'),
    name: 'date',
    sortable: true,
  },
  {
    field: 'dueDate',
    filter: { type: 'date' },
    format: value => value && format(value),
    label: t('afaktoApp.invoice.dueDate'),
    name: 'dueDate',
    sortable: true,
  },
  {
    field: 'currency',
    filter: { type: 'currency' },
    hidden: true,
    label: t('afaktoApp.invoice.currency'),
    name: 'currency',
    sortable: true,
  },
  {
    classes: props => (props.amount < 0 ? 'text-negative' : 'text-positive'),
    field: 'amount',
    filter: { type: 'number' },
    format: (value, row) => n(value, 'currency', { currency: row.currency }),
    label: t('afaktoApp.invoice.amount'),
    name: 'amount',
    sortable: true,
  },
  {
    classes: props => (props.balance < 0 ? 'text-negative' : 'text-positive'),
    field: 'balance',
    filter: { type: 'number' },
    format: (value, row) => n(value, 'currency', { currency: row.currency }),
    label: t('afaktoApp.invoice.balance'),
    name: 'balance',
    sortable: true,
  },
  {
    align: 'center',
    field: 'underFactor',
    filter: { type: 'boolean' },
    label: t('afaktoApp.invoice.underFactor'),
    name: 'isUnderFactor',
    sortable: true,
  },
  {
    align: 'left',
    field: row => row.gap?.type,
    filter: { type: 'boolean' },
    label: t('afaktoApp.invoice.gap'),
    name: 'gap',
    sortable: true,
  },
  {
    classes: props => (props.invoiceFromFactor?.amountFunded < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.invoiceFromFactor?.amountFunded,
    filter: { type: 'number' },
    format: (value, row) => value && n(value, 'currency', { currency: row.currency }),
    label: t('afaktoApp.invoice.invoiceFromFactor.amountFunded'),
    name: 'invoiceFromFactor_amountFunded',
    sortable: true,
  },
  {
    classes: props => (props.invoiceFromFactor?.amountUnsecured < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.invoiceFromFactor?.amountUnsecured,
    filter: { type: 'number' },
    format: (value, row) => value && n(value, 'currency', { currency: row.currency }),
    hidden: true,
    label: t('afaktoApp.invoice.invoiceFromFactor.amountUnsecured'),
    name: 'invoiceFromFactor_amountUnsecured',
    sortable: true,
  },
]);

const visibleColumns = ref([]);
const filters = ref({ balance: { operation: 'notEquals', value: 0 } });
watch(filters, () => onRequest({ pagination: pagination.value }), { deep: true });

const pagination = ref({
  sortBy: route.query.sortBy || 'date',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15,
});

const rows = ref([]);

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  const response = await api.get(baseApiUrl, {
    params: {
      page: page - 1,
      size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
      'buyer.equals': route.params.id,
      ...filtersToQueryParams(filters.value),
    },
  });
  rows.value = response.data;

  pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
};
</script>
