<template>
  <template v-for="product of modelValue.buyerFromInsurer.raw.products">
    <template v-if="product.deliveryStatus">
      <p v-if="product.endDate && new Date(product.endDate) < new Date()">
        <q-icon class="text-red text-h4" name="o_warning" />
        {{ $t('afaktoApp.buyer.buyerFromInsurer.obsolete') }}
      </p>

      <q-table
        :rows="rows(product)"
        :columns="columns"
        row-key="label"
        hide-header
        flat
        bordered
        separator="cell"
        :hide-pagination="true"
        :pagination="{ rowsPerPage: 0 }"
        class="q-mx-auto"
        style="max-width: 50em"
      >
        <template v-slot:body-cell="props" style="width: 50%">
          <q-td :props="props" class="q-pa-none">
            <div v-if="props.col.name === 'label'" class="text-weight-bold" style="text-align: left">
              {{ props.row.label }}
            </div>

            <div v-else-if="props.col.name === 'value'" :style="{ textAlign: props.row.align }">
              {{ props.row.value }}
            </div>
          </q-td>
        </template>
      </q-table>
    </template>
  </template>
</template>

<script setup>
import { api } from 'boot/axios.js';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { format } from 'src/util/format';
import useNotifications from 'src/util/useNotifications.js';

const contract = ref(null);
const model = defineModel({ type: Object });
const { n, t } = useI18n();
const { notifyError } = useNotifications();

const rows = computed(() => product => [
  { label: 'Type', value: t(`afaktoApp.creditLimit.insurer.coface.productType.${product.productCode}`), align: 'left' },
  { label: 'Decision', value: t(`afaktoApp.creditLimit.insurer.coface.deliveryStatus.${product.deliveryStatus}`), align: 'left' },
  {
    label: 'Approved Amount',
    value: n(product.position.amount.value, 'currencyCode', { currency: product.position.amount.currency }),
    align: 'right',
  },
  { label: 'Decision date', value: format(product.updateDate, 'yyyy-MM-dd'), align: 'right' },
  { label: 'Effect from date', value: format(product.effectDate, 'yyyy-MM-dd'), align: 'right' },
  { label: 'Credit insurance', value: 'COFACE', align: 'left' },
  { label: 'Buyer insurance Id', value: model.value.buyerFromInsurer.insurerCode, align: 'left' },
  { label: 'Policy Id', value: contract.value?.creditInsurancePolicy?.policyId, align: 'left' },
]);

const columns = [
  { name: 'label', required: true, label: 'Label', field: 'label' },
  { name: 'value', required: true, label: 'Value', field: 'value' },
];

onMounted(async () => {
  try {
    const response = await api.get(`/api/buyers/${model.value.id}/contract`);
    contract.value = response.data;
  } catch (error) {
    notifyError(error);
  }
});
</script>
