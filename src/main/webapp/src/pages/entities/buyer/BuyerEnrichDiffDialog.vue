<template>
  <q-dialog :model-value="modelValue" @update:model-value="val => $emit('update:modelValue', val)" persistent>
    <q-card style="min-width: 350px">
      <q-toolbar>
        <div class="text-h6">{{ t('afaktoApp.buyer.enrichedChangesTitle') }}</div>
        <q-space />
        <q-btn class="buttonNeutral" icon="close" to="#" @click="closeDialog">
          <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
        </q-btn>
      </q-toolbar>

      <q-card-section v-if="showCountrySelector">
        <div class="text-subtitle1 q-mb-sm">{{ t('afaktoApp.buyer.enrichCountrySelect') }}</div>
        <q-list bordered separator>
          <q-item v-for="option in countryOptions" :key="option.code" clickable @click="onCountrySelected(option.code)">
            <q-item-section class="column items-center no-wrap">
              <span :class="`fi fi-${option.code}`"></span>
              <span>{{ t(`afaktoApp.NumberType.${option.code.toUpperCase()}.${props.countryFilter[option.code].type}.label`) }}</span>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>

      <q-card-section v-else-if="!enriching">
        <div v-if="changedFields.length || props.entity.numberType !== tmp.numberType || props.entity.number !== tmp.number">
          <div
            v-if="props.entity.numberType !== tmp.numberType || props.entity.number !== tmp.number"
            class="row justify-around items-center q-gutter-md q-mb-md"
          >
            <div class="col">
              <div class="text-caption text-grey">{{ t('afaktoApp.buyer.enrichPreviousNumber') }}</div>
              <div class="text-body1">{{ props.entity.number }}</div>

              <q-badge
                v-if="
                  props.entity.address?.country &&
                  tm(`afaktoApp.NumberType.${props.entity.address.country.toUpperCase()}.${props.entity.numberType}`)?.label
                "
                color="blue"
              >
                {{ t(`afaktoApp.NumberType.${tmp.address.country.toUpperCase()}.${tmp.numberType}.label`) }}
                <q-tooltip>
                  {{ $t(`afaktoApp.NumberType.${tmp.numberType}`).replaceAll('_', ' ') }}
                </q-tooltip>
              </q-badge>
              <q-badge v-else color="blue">{{ $t(`afaktoApp.NumberType.${props.entity.numberType}`).replaceAll('_', ' ') }}</q-badge>
            </div>
            <div class="col-auto flex items-center">
              <q-icon name="arrow_forward" size="md" class="text-grey-7" />
            </div>
            <div class="col">
              <div class="text-caption text-grey">{{ t('afaktoApp.buyer.enrichNewNumber') }}</div>
              <div class="text-body1">{{ tmp.number }}</div>
              <q-badge color="blue">
                {{ t(`afaktoApp.NumberType.${tmp.address.country.toUpperCase()}.${tmp.numberType}.label`) }}
                <q-tooltip>
                  {{ $t(`afaktoApp.NumberType.${tmp.numberType}`).replaceAll('_', ' ') }}
                </q-tooltip>
              </q-badge>
            </div>
          </div>
          <div class="row justify-around items-stretch q-gutter-md">
            <div class="col">
              <address-comp :model-value="originalAddress" :readonly="true" />
            </div>

            <div class="col-auto flex items-center">
              <q-icon name="arrow_forward" size="xl" class="text-grey-7" />
            </div>

            <div class="col">
              <address-comp :model-value="enrichedAddress" :readonly="false" />
            </div>
          </div>
        </div>
        <div v-else class="text-center text-grey">
          {{ t('afaktoApp.buyer.noChanges') }}
        </div>
      </q-card-section>

      <q-card-section v-else> Loading... </q-card-section>

      <q-card-actions align="center">
        <q-btn
          class="buttonBrand"
          color="primary"
          icon="label_important"
          :label="$t('entity.action.save')"
          :loading="loading"
          type="submit"
          @click="onSubmit(entity, enrichedAddress)"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import BuyerService from 'pages/entities/buyer/buyer.service';
import AddressComp from 'pages/subcomponents/AddressComp.vue';
import useNotifications from 'src/util/useNotifications';

const closeDialog = () => emit('update:modelValue', false);
const emit = defineEmits(['update:modelValue']);
const loading = ref(false);
const { notifyError } = useNotifications();
const { t, tm } = useI18n();

const props = defineProps({
  modelValue: Boolean,
  entity: Object,
  countryFilter: Object,
  identifierType: Object,
});

const tmp = ref({
  address: { country: props.entity.address?.country },
  numberType: props.entity.numberType,
  number: props.entity.number,
  company: props.entity.company,
  name: props.entity.name,
});
const showCountrySelector = ref(false);
const originalAddress = ref({});
const enrichedAddress = ref({});
const changedFields = ref([]);
const enriching = ref(false);
const countryOptions = ref({});

onMounted(async () => {
  if (!props.entity?.number == null || props.entity.number === '0') {
    tmp.value.numberType = null;
    return searchLegalEntity();
  }

  const countryKeys = Object.keys(props.countryFilter);

  if (Object.keys(props.identifierType).length) {
    tmp.value.address.country = props.identifierType.country;
    tmp.value.numberType = props.identifierType.type;
    searchLegalEntity();
  } else if (countryKeys.length > 1) {
    countryOptions.value = countryKeys.map(code => ({
      code,
      ...props.countryFilter[code],
    }));
    showCountrySelector.value = true;
  } else closeDialog();
});

async function onCountrySelected(code) {
  tmp.value.address.country = code;
  tmp.value.numberType = props.countryFilter[code].type;
  showCountrySelector.value = false;
  await searchLegalEntity();
}

async function searchLegalEntity() {
  enriching.value = true;

  try {
    const enrichedBuyer = await BuyerService.search(tmp.value);
    console.log(enrichedBuyer);
    if (!enrichedBuyer.data.buyers.length) {
      notifyError(t(`afaktoApp.buyer.enrichmentErrors.NOT_FOUND`));
      return closeDialog();
    }

    originalAddress.value = props.entity.address || {};
    enrichedAddress.value = enrichedBuyer.data.buyers[0].address || {};

    tmp.value.numberType = enrichedBuyer.data.buyers[0].numberType;
    tmp.value.number = enrichedBuyer.data.buyers[0].number;

    changedFields.value = computeAddressDiffs(props.entity.address, enrichedAddress.value);
    enriching.value = false;
  } catch (err) {
    if (err.response?.status === 400 && err.response.data?.errorCode)
      notifyError(t(`afaktoApp.buyer.enrichmentErrors.${err.response.data.errorCode}`));
    else notifyError(err);
    closeDialog();
  }
}

const fields = ['streetName', 'streetNumber', 'postalCode', 'city', 'stateProvince', 'country'];

const computeAddressDiffs = (original, enriched) =>
  fields
    .filter(field => {
      const oldVal = original?.[field] ?? '';
      const newVal = enriched?.[field] ?? null;
      return newVal !== null && oldVal !== newVal;
    })
    .map(field => ({ field }));

const onSubmit = async (entity, enrichedAddress) => {
  loading.value = true;

  entity.address = enrichedAddress;
  entity.number = tmp.value.number;
  entity.numberType = tmp.value.numberType;

  BuyerService.save(entity)
    .catch(error => notifyError(error))
    .finally(() => {
      loading.value = false;
      closeDialog();
    });
};
</script>
