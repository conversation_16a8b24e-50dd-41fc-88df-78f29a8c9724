<template>
  <q-toolbar>
    <q-space />
    <q-btn v-if="hasRoleAdmin" icon="loupe" label="Raw" @click="showRawData = true" />
    <q-btn v-if="hasRoleWriter" icon="sync" :label="$t('entity.action.update')" @click="updateCreditLimit" />
  </q-toolbar>

  <q-dialog v-model="showRawData">
    <q-card>
      <q-card-section>
        <q-btn class="float-right" color="primary" icon="close" @click="showRawData = false" />
        <div class="text-h3">Raw Data</div>
      </q-card-section>

      <json-viewer :model-value="modelValue.buyerFromInsurer.raw" />
    </q-card>
  </q-dialog>

  <buyer-details-from-insurer-atradius
    v-if="modelValue.buyerFromInsurer.insurerName === 'atradius' && modelValue.buyerFromInsurer.raw"
    :model-value="modelValue.buyerFromInsurer.raw"
  />
  <buyer-details-from-insurer-coface
    v-if="modelValue.buyerFromInsurer.insurerName === 'coface' && modelValue.buyerFromInsurer.raw"
    :model-value="modelValue"
  />

  <q-inner-loading :showing="updating">
    <q-spinner-gears color="primary" size="10em" />
  </q-inner-loading>
</template>

<script setup>
import { api } from 'boot/axios';
import { useQuasar } from 'quasar';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';

import JsonViewer from 'pages/subcomponents/JsonViewer.vue';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { useInsurerCreditLimitStore } from 'src/stores/insurer-credit-limit-store.ts';
import useNotifications from 'src/util/useNotifications';

import BuyerDetailsFromInsurerAtradius from './BuyerDetailsFromInsurerAtradius.vue';
import BuyerDetailsFromInsurerCoface from './BuyerDetailsFromInsurerCoface.vue';

const baseApiUrl = '/api/buyers';
const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const hasRoleAdmin = useAuthenticationStore().hasRoleAdmin;
const { notifyError } = useNotifications();
const $q = useQuasar();
const route = useRoute();
const showRawData = ref(false);
const { t } = useI18n();
const updating = ref(false);

const modelValue = defineModel({ type: Object });

const updateCreditLimit = async () => {
  updating.value = true;
  try {
    const response = await api.post(`${baseApiUrl}/${route.params.id}/updateCreditLimit`);

    const updated = modelValue.value?.creditLimit?.version != response.data?.creditLimit?.version;

    modelValue.value = response.data;
    if (updated)
      $q.notify({
        color: 'positive',
        icon: 'sync',
        message: t('afaktoApp.buyer.buyerFromInsurer.updatedCreditLimit'),
      });
    else
      $q.notify({
        color: 'info',
        icon: 'sync',
        message: t('afaktoApp.buyer.buyerFromInsurer.noChange'),
      });

    useInsurerCreditLimitStore().markUpdated();
  } catch (error) {
    notifyError(error);
  } finally {
    updating.value = false;
  }
};
</script>
