<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.company.home.title') }}</h1>

      <q-space />

      <q-btn
        v-if="entity.id"
        class="buttonNeutral"
        icon="delete"
        :label="$t('entity.action.delete')"
        @click="onDelete"
        :disabled="!isDeletable"
        :title="!isDeletable ? 'There is data associated with this company, you can’t delete it' : ''"
      />
      <q-btn class="buttonNeutral" icon="close" to="/companies">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-toolbar>

    <q-form greedy @submit="onSubmit">
      <q-tabs v-model="tab" :vertical="$q.platform.is?.desktop">
        <q-tab name="main" :label="t('afaktoApp.company.detail.title')" />
        <q-tab name="address" :label="t('afaktoApp.company.detail.address')" />
      </q-tabs>

      <q-tab-panels v-model="tab" animated swipeable :vertical="$q.platform.is?.desktop">
        <q-tab-panel name="main">
          <q-select
            v-if="organizations.length"
            v-model="entity.orgId"
            class="required"
            emit-value
            filled
            :label="$t('afaktoApp.company.organization')"
            map-options
            option-label="displayName"
            option-value="orgId"
            options-selected-class="text-bold"
            :options="organizations"
            :readonly="!!entity.id"
            :rules="[required]"
          />

          <b-input v-model="entity.code" class="required" :label="$t('afaktoApp.company.code')" :rules="[required]" />
          <b-input v-model="entity.name" class="required" :label="$t('afaktoApp.company.name')" :rules="[required]" />
          <q-input
            v-model="entity.number"
            fill-mask
            filled
            :label="$t('afaktoApp.company.number')"
            :mask="NUMBER_TYPE_MASKS[entity.numberType]"
            unmasked-value
          >
            <template #prepend>
              <q-select
                v-model="entity.numberType"
                bg-color="transparent"
                :label="$t('afaktoApp.company.numberType')"
                :options="NUMBER_TYPES"
                :option-label="numberType => $t(`afaktoApp.NumberType.${numberType}`).replaceAll('_', ' ')"
                style="min-width: 12em"
              />
            </template>
          </q-input>

          <q-select
            v-model="entity.fiscalYearClosingMonth"
            emit-value
            filled
            :label="$t('afaktoApp.company.fiscalYearClosingMonth')"
            map-options
            :options="[{ value: null, label: '' }, ...monthOptions]"
          />
        </q-tab-panel>

        <q-tab-panel name="address">
          <address-comp v-model="entity.address" />
        </q-tab-panel>
      </q-tab-panels>

      <div class="full-width text-center">
        <q-btn class="buttonBrand" icon="label_important" :label="$t('entity.action.save')" type="submit" />
      </div>
    </q-form>

    <entity-meta :entity="entity" />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { useQuasar } from 'quasar';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import AddressComp from 'pages/subcomponents/AddressComp.vue';
import EntityMeta from 'pages/subcomponents/EntityMeta.vue';
import { required } from 'src/components/rules';
import { NUMBER_TYPES, NUMBER_TYPE_MASKS } from 'src/constants/numberType';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { setupBeforeUnload } from 'src/util/formBeforeUnload';
import useNotifications from 'src/util/useNotifications';

const baseApiUrl = '/api/companies';
const hasRoleAdmin = useAuthenticationStore().hasRoleAdmin;
const monthOptions = [...Array(12).keys()].map(i => ({
  label: new Date(0, i).toLocaleString('default', { month: 'long' }),
  value: i + 1,
}));
const { notifyError } = useNotifications();
const organizations = ref([]);
const $q = useQuasar();
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const tab = ref(route.query.tab || 'main');
const isDeletable = ref(true);

const entity = ref({
  address: { city: '' },
});

onMounted(async () => {
  if (hasRoleAdmin) organizations.value = (await api.get('/api/organizations')).data;
  if (route.params?.id) {
    try {
      entity.value = (await api.get(`${baseApiUrl}/${route.params.id}`)).data;

      // Check if the company is deletable
      const response = await api.get(`/api/companies/deletable/${route.params.id}`);
      isDeletable.value = response.data;
    } catch (error) {
      notifyError(error);
    }
  }
  setupBeforeUnload(t, document.forms[0], entity.value);
});

const onSubmit = async () => {
  try {
    await api({
      method: entity.value.id ? 'put' : 'post',
      baseURL: baseApiUrl,
      url: entity.value.id,
      data: entity.value,
    });
    router.back();
  } catch (error) {
    notifyError(error);
  }
};

const onDelete = () => {
  $q.dialog({
    message: t('afaktoApp.company.delete.question', { id: entity.value.name }),
    cancel: true,
  }).onOk(() => {
    api
      .delete(`${baseApiUrl}/${entity.value.id}`)
      .then(() => {
        $q.notify({
          message: 'Deleted',
          icon: 'announcement',
        });
        router.back();
      })
      .catch(error => notifyError(error));
  });
};
</script>
