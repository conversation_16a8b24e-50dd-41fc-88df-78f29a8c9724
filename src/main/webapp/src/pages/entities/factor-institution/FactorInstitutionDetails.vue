<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.factorInstitution.detail.title') }}</h1>

      <q-space />

      <q-btn v-if="entity.id" class="buttonNeutral" icon="delete" :label="$t('entity.action.delete')" @click="onDelete" />
      <q-btn class="buttonNeutral" icon="close" to="/factor-institutions">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-toolbar>

    <q-form greedy @submit="onSubmit">
      <q-card>
        <q-card-section>
          <q-select
            v-model="entity.type"
            class="required"
            filled
            :label="$t('afaktoApp.factorInstitution.type')"
            :options="['BANKING', 'NON_BANKING']"
            :rules="[required]"
          />
          <b-input v-model="entity.code" :label="$t('afaktoApp.factorInstitution.code')" />
          <b-input v-model="entity.name" class="required" :label="$t('afaktoApp.factorInstitution.name')" :rules="[required]" />
        </q-card-section>

        <q-card-actions align="center">
          <q-btn class="buttonBrand" icon="label_important" :label="$t('entity.action.save')" type="submit" />
        </q-card-actions>
      </q-card>
    </q-form>

    <entity-meta :entity="entity" />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { useQuasar } from 'quasar';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import EntityMeta from 'pages/subcomponents/EntityMeta.vue';
import { required } from 'src/components/rules';
import { setupBeforeUnload } from 'src/util/formBeforeUnload';
import useNotifications from 'src/util/useNotifications';

const baseApiUrl = '/api/factor-institutions';
const { notifyError } = useNotifications();
const $q = useQuasar();
const router = useRouter();
const route = useRoute();
const { t } = useI18n();

let entity = ref({});

onMounted(async () => {
  if (route.params?.id) {
    try {
      entity.value = (await api.get(`${baseApiUrl}/${route.params.id}`)).data;
    } catch (error) {
      notifyError(error);
    }
  }

  setupBeforeUnload(t, document.forms[0], entity.value);
});

const onSubmit = async () => {
  try {
    await api({
      method: entity.value.id ? 'put' : 'post',
      baseURL: baseApiUrl,
      url: entity.value.id,
      data: entity.value,
    });
    router.back();
  } catch (error) {
    notifyError(error);
  }
};

const onDelete = () => {
  $q.dialog({
    message: t('afaktoApp.factorInstitution.delete.question', { id: entity.value.name }),
    cancel: true,
  }).onOk(() => {
    api
      .delete(`${baseApiUrl}/${entity.value.id}`)
      .then(() => {
        $q.notify({
          message: 'Deleted',
          icon: 'announcement',
        });
        router.back();
      })
      .catch(error => notifyError(error));
  });
};
</script>
