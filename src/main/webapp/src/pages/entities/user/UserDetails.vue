<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.user.detail.title') }}</h1>

      <q-space />

      <q-btn v-if="entity.id" class="buttonNeutral" icon="delete" :label="$t('entity.action.delete')" @click="onDelete" />
      <q-btn class="buttonNeutral" icon="close" to=".">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-toolbar>

    <q-form greedy @submit="onSubmit">
      <q-card>
        <q-card-section>
          <q-select
            v-if="organizations.length"
            v-model="entity.orgId"
            class="required"
            emit-value
            filled
            :label="$t('afaktoApp.user.organization')"
            map-options
            option-label="displayName"
            option-value="orgId"
            options-selected-class="text-bold"
            :options="organizations"
            :readonly="!!entity.id"
            :rules="[required]"
          />

          <b-input
            v-model="entity.login"
            class="required"
            :label="$t('afaktoApp.user.login')"
            :readonly="!!entity.id"
            :rules="[required]"
            type="email"
          />
          <b-input v-model="entity.email" :label="$t('afaktoApp.user.email')" type="email" />
          <b-input v-model="entity.firstName" :label="$t('afaktoApp.user.firstName')" />
          <b-input v-model="entity.lastName" :label="$t('afaktoApp.user.lastName')" />

          <q-field filled :label="$t('afaktoApp.user.authorities')" stack-label>
            <q-option-group
              v-model="entity.authorities"
              :options="
                authorities.map(auth => ({
                  value: auth,
                  label: $t(`afaktoApp.authority.values.${auth}.label`) + ' - ' + $t(`afaktoApp.authority.values.${auth}.help`),
                }))
              "
              type="toggle"
            />
          </q-field>

          <b-selector
            v-model="entity.companies"
            :label="$t('afaktoApp.user.companies')"
            :multiple="true"
            option-label="name"
            :option-value="option => option"
            url="/api/companies?sort=name"
          />

          <b-currency v-model="entity.preferences.REFERENCE_CURRENCY" :label="$t('afaktoApp.user.me.referenceCurrency')" />
        </q-card-section>

        <q-card-actions align="center">
          <q-btn class="buttonBrand" icon="label_important" :label="$t('entity.action.save')" type="submit" />
        </q-card-actions>
      </q-card>
    </q-form>

    <entity-meta :entity="entity" />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { useQuasar } from 'quasar';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import EntityMeta from 'pages/subcomponents/EntityMeta.vue';
import { required } from 'src/components/rules';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { setupBeforeUnload } from 'src/util/formBeforeUnload';
import useNotifications from 'src/util/useNotifications.js';

const baseApiUrl = '/api/users';
const hasRoleAdmin = useAuthenticationStore().hasRoleAdmin;
const { notifyError } = useNotifications();
const $q = useQuasar();
const router = useRouter();
const route = useRoute();
const store = useAuthenticationStore();
const { t } = useI18n();

const authorities = ref([]);
const entity = ref({ authorities: [], preferences: {} });
const organizations = ref([]);

onMounted(async () => {
  authorities.value = (await api.get('/api/authorities')).data;
  if (hasRoleAdmin) organizations.value = (await api.get('/api/organizations')).data;
  if (route.params?.id) {
    try {
      entity.value = (await api.get(`${baseApiUrl}/${route.params.id}`)).data;
    } catch (error) {
      notifyError(error);
    }
  }

  setupBeforeUnload(t, document.forms[0], entity.value);
});

const onSubmit = async () => {
  try {
    const response = await api({
      method: entity.value.id ? 'put' : 'post',
      baseURL: baseApiUrl,
      url: entity.value.id?.toString(),
      data: entity.value,
    });

    if (store.account?.login == entity.value.login) {
      // Update the currently logged in user
      store.login(response.data);
    }
    router.back();
  } catch (error) {
    notifyError(error);
  }
};

const onDelete = () => {
  $q.dialog({
    message: t('afaktoApp.user.delete.question', { login: entity.value.login }),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    try {
      api.delete(`${baseApiUrl}/${entity.value.id}`).then(() => {
        $q.notify({
          message: t('afaktoApp.user.deleted', { param: entity.value.login }),
          icon: 'announcement',
        });
        router.back();
      });
    } catch (error) {
      notifyError(error);
    }
  });
};
</script>
