<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.invoice.home.title') }}</h1>

      <q-space />

      <q-btn v-if="hasRoleWriter && entity.id" class="buttonNeutral" icon="delete" :label="$t('entity.action.delete')" @click="onDelete" />
      <q-btn class="buttonNeutral" icon="close" to=".">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-toolbar>

    <q-form greedy @submit="onSubmit">
      <q-tabs v-model="tab" :vertical="$q.platform.is?.desktop">
        <q-tab name="main" :label="t('afaktoApp.invoice.detail.title')" />
        <q-tab v-if="cessions.length" name="cession" :label="t('afaktoApp.invoice.detail.cession')" />
        <q-tab v-if="entity.invoiceFromFactor" name="factor" :label="t('afaktoApp.invoice.detail.factor')" />
      </q-tabs>

      <q-tab-panels v-model="tab" animated swipeable :vertical="$q.platform.is?.desktop">
        <q-tab-panel name="main">
          <b-selector
            v-if="!entity.id"
            v-model="entity.buyer"
            class="required"
            :label="$t('afaktoApp.invoice.buyer')"
            option-label="name"
            option-value="id"
            :readonly="!!entity.id"
            :rules="[required]"
            url="/api/buyers?sort=name&size=1000"
          />
          <q-input v-else v-model="entity.buyer.name" filled :label="$t('afaktoApp.invoice.buyer')" readonly>
            <template #append>
              <q-icon class="cursor-pointer" name="north_east" @click="router.push(`/buyers/${entity.buyer.id}?tab=invoices`)" />
            </template>
          </q-input>
          <q-select
            v-model="entity.type"
            class="required"
            filled
            :label="$t('afaktoApp.invoice.type')"
            :options="Object.values(InvoiceType)"
            :option-label="item => $t(`afaktoApp.InvoiceType.${item}`)"
            :readonly="!hasRoleWriter"
            :rules="[required]"
          />

          <b-input
            v-model="entity.invoiceNumber"
            class="required"
            :label="$t('afaktoApp.invoice.invoiceNumber')"
            :readonly="!hasRoleWriter"
            :rules="[required]"
          />
          <q-input v-model="entity.date" filled :label="$t('afaktoApp.invoice.date')" :readonly="!hasRoleWriter" type="date" />
          <q-input
            v-model="entity.dueDate"
            filled
            :label="$t('afaktoApp.invoice.dueDate')"
            :readonly="!hasRoleWriter"
            type="date"
            :rules="[val => dueDateAfterInvoiceDate(val, entity.date)]"
          />

          <b-currency
            v-model="entity.currency"
            class="required"
            filled
            :label="$t('afaktoApp.invoice.currency')"
            :readonly="!hasRoleWriter || !!entity.buyer?.currency"
            :rules="[required]"
          />

          <b-input
            v-model="entity.amount"
            :label="$t('afaktoApp.invoice.amount')"
            :readonly="!hasRoleWriter"
            :rules="[val => validAmountForType(val, entity.type)]"
            type="currency"
          />
          <b-input
            v-model="entity.balance"
            :label="$t('afaktoApp.invoice.balance')"
            :readonly="!hasRoleWriter"
            type="currency"
            :rules="[
              val => validBalanceForType(val, entity.type),
              val => balanceLessThanAmount(val, entity.amount),
              val => sameSignAmountAndBalance(val, entity.amount),
            ]"
          />

          <b-input
            v-if="entity.balance == 0 || entity.reconciliationJournal"
            v-model="entity.reconciliationJournal"
            :label="$t('afaktoApp.invoice.reconciliationJournal')"
            :readonly="!hasRoleWriter"
          />

          <q-select
            v-if="entity.gap"
            v-model="entity.gap.type"
            filled
            :label="$t('afaktoApp.invoice.gap')"
            map-options
            :options="
              GAP_TYPES.map(type => ({
                label:
                  $t(`afaktoApp.invoice.gapType_values.${type}`) + ' - ' + $t(`afaktoApp.invoice.gapCredit_values.${entity.gap.credit}`),
                value: type,
              }))
            "
            readonly
          >
            <template #prepend>
              <q-icon color="warning" name="bolt" size="xl" />
            </template>
          </q-select>

          <b-toggle v-model="entity.underFactor" disable :label="$t('afaktoApp.invoice.underFactor')" />
        </q-tab-panel>

        <q-tab-panel name="cession">
          <q-table
            binary-state-sort
            :columns="columns"
            hide-bottom
            row-key="id"
            :rows-per-page-options="[0]"
            :rows="cessions"
            @row-click="(_, { id }) => router.push(`/cessions/${id}`)"
          />
        </q-tab-panel>

        <q-tab-panel v-if="entity.invoiceFromFactor" name="factor">
          <b-input
            input-class="text-right"
            :label="$t('afaktoApp.invoice.invoiceFromFactor.amount')"
            :model-value="$n(entity.invoiceFromFactor.amount, 'currencyCode', { currency: entity.invoiceFromFactor.currency })"
            readonly
          />
          <b-input
            input-class="text-right"
            :label="$t('afaktoApp.invoice.invoiceFromFactor.balance')"
            :model-value="$n(entity.invoiceFromFactor.balance, 'currencyCode', { currency: entity.invoiceFromFactor.currency })"
            readonly
          />

          <b-input
            input-class="text-right"
            :label="$t('afaktoApp.invoice.invoiceFromFactor.amountDraftReceived')"
            :model-value="$n(entity.invoiceFromFactor.amountDraftReceived, 'currencyCode', { currency: entity.invoiceFromFactor.currency })"
            readonly
          />
          <q-input
            v-if="entity.invoiceFromFactor.draftDueDate"
            v-model="entity.invoiceFromFactor.draftDueDate"
            filled
            :label="$t('afaktoApp.invoiceFromFactor.draftDueDate')"
            readonly
            type="date"
          />

          <b-input
            input-class="text-right"
            :label="$t('afaktoApp.invoice.invoiceFromFactor.amountFunded')"
            :model-value="$n(entity.invoiceFromFactor.amountFunded, 'currencyCode', { currency: entity.invoiceFromFactor.currency })"
            readonly
          />
          <b-input
            input-class="text-right"
            :label="$t('afaktoApp.invoice.invoiceFromFactor.amountUnfunded')"
            :model-value="$n(entity.invoiceFromFactor.amountUnfunded, 'currencyCode', { currency: entity.invoiceFromFactor.currency })"
            readonly
          />
          <b-input
            input-class="text-right"
            :label="$t('afaktoApp.invoice.invoiceFromFactor.amountUnavailable')"
            :model-value="$n(entity.invoiceFromFactor.amountUnavailable, 'currencyCode', { currency: entity.invoiceFromFactor.currency })"
            readonly
          />

          <entity-meta-dates :entity="entity.invoiceFromFactor" />
        </q-tab-panel>
      </q-tab-panels>

      <div class="full-width text-center">
        <q-btn v-if="hasRoleWriter" class="buttonBrand" icon="label_important" :label="$t('entity.action.save')" type="submit" />
      </div>
    </q-form>

    <entity-meta :entity="entity" />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { useQuasar } from 'quasar';
import { computed, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import EntityMeta from 'pages/subcomponents/EntityMeta.vue';
import EntityMetaDates from 'pages/subcomponents/EntityMetaDates.vue';
import { required } from 'src/components/rules';
import {
  balanceLessThanAmount,
  dueDateAfterInvoiceDate,
  sameSignAmountAndBalance,
  validAmountForType,
  validBalanceForType,
} from 'src/components/rules/invoiceRules';
import { GAP_TYPES } from 'src/constants/gapType.js';
import { InvoiceType } from 'src/shared/model/enumerations/invoice-type.model';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { setupBeforeUnload } from 'src/util/formBeforeUnload';
import useNotifications from 'src/util/useNotifications.js';

const baseApiUrl = '/api/invoices';
const { d, n, t } = useI18n();
const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const { notifyError } = useNotifications();
const $q = useQuasar();
const route = useRoute();
const router = useRouter();
const tab = ref(route.query.tab || 'main');

const entity = ref({
  amount: 0,
  balance: 0,
});
const cessions = ref([]);

const columns = computed(() => [
  {
    align: 'left',
    field: row => row.contract.factorInstitution.name,
    label: t('afaktoApp.factorInstitution.detail.title'),
    name: 'contract.factorInstitution.name',
    sortable: true,
  },
  {
    align: 'center',
    field: 'createdDate',
    format: value => d(new Date(value), 'long'),
    label: t('global.field.createdDate'),
    name: 'createdDate',
    sortable: true,
  },
  {
    field: 'count',
    format: value => n(value),
    label: t('afaktoApp.cession.count'),
    name: 'count',
    sortable: true,
  },
  {
    field: row => row.contract.financialInformation.currency,
    label: t('afaktoApp.cession.currency'),
    name: 'contract.financialInformation.currency',
    sortable: true,
  },
  {
    classes: props => (props.sum < 0 ? 'text-negative' : 'text-positive'),
    field: 'sum',
    format: (value, row) => n(value, 'currency', { currency: row.contract.financialInformation.currency }),
    label: t('afaktoApp.cession.sum'),
    name: 'sum',
    sortable: true,
  },
]);

onMounted(async () => {
  if (route.params?.id) {
    try {
      entity.value = (await api.get(`${baseApiUrl}/${route.params.id}`)).data;
    } catch (err) {
      notifyError(err);
    }

    try {
      cessions.value = (await api.get('/api/cessions', { params: { 'invoice.equals': route.params.id } })).data;
    } catch (err) {
      notifyError(err);
    }
  }

  setupBeforeUnload(t, document.forms[0], entity.value);
});

watch(
  () => entity.value.buyer,
  buyer => (entity.value.currency = entity.value.currency || buyer.currency),
);

const onSubmit = async () => {
  try {
    await api({
      method: entity.value.id ? 'put' : 'post',
      baseURL: baseApiUrl,
      url: entity.value.id?.toString(),
      data: entity.value,
    });
    router.back();
  } catch (error) {
    notifyError(error);
  }
};

const onDelete = () => {
  $q.dialog({
    message: t('afaktoApp.invoice.delete.question', { id: entity.value.invoiceNumber }),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    api
      .delete(`${baseApiUrl}/${entity.value.id}`)
      .then(() => {
        $q.notify({
          message: t('afaktoApp.invoice.deleted', { param: entity.value.invoiceNumber }),
          icon: 'announcement',
        });
        router.back();
      })
      .catch(error => notifyError(error));
  });
};
</script>
