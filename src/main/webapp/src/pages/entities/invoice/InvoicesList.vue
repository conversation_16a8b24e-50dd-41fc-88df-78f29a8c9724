<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.invoice.home.title') }}</h1>

      <q-space />

      <div class="buttonDiv">
        <q-btn
          class="buttonNeutral"
          v-if="hasRoleConfig"
          icon="o_file_upload"
          :label="$t('entity.action.import')"
          to="/datastreams/upload?type=INVOICE"
        />

        <rows-export
          :base-api-url="baseApiUrl"
          class="buttonNeutral"
          :columns="columns"
          :filters="filters"
          :pagination="pagination"
          :visible-columns="visibleColumns"
        />

        <div class="btn-separator" />

        <q-btn class="buttonBrand" v-if="hasRoleWriter" icon="add" :label="$t('afaktoApp.invoice.home.createLabel')" to="/invoices/new" />
      </div>
    </q-toolbar>

    <q-toolbar>
      <columns-filtering v-model="filters" :columns="columns" />
      <columns-visibility v-model="visibleColumns" :columns="columns.filter(col => 'hasCover' != col.name)" />
    </q-toolbar>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      :columns="columns"
      row-key="id"
      :rows="rows"
      :rows-per-page-options="[0]"
      :visible-columns="visibleColumns"
      @request="onRequest"
      @row-click="(_event, { id }) => router.push(`/invoices/${id}`)"
    >
      <template #header-cell-buyerExcluded>
        <q-th class="boolean" />
      </template>
      <template #header-cell-gap>
        <q-th class="boolean" />
      </template>
      <template #header-cell-gapCredit>
        <q-th align="left">{{ $t('afaktoApp.invoice.gapCredit') }}</q-th>
      </template>
      <template #body-cell-isUnderFactor="props">
        <q-td :props="props">
          <q-icon v-if="!!props.value" color="blue" name="task_alt" size="md" />
        </q-td>
      </template>
      <template #body-cell-gap="props">
        <q-td class="boolean">
          <q-icon v-if="props.value" color="warning" name="bolt" size="md"> </q-icon>
          <q-tooltip v-if="props.value">
            {{ $t('afaktoApp.invoice.gap') }}
          </q-tooltip>
        </q-td>
      </template>
      <template #body-cell-buyerExcluded="props">
        <q-td class="boolean">
          <q-icon v-if="!!props.value" color="warning" name="remove_done" size="sm">
            <q-tooltip v-if="props.row.buyer.exclusionReason" style="white-space: pre-line">
              {{ props.row.buyer.exclusionReason }}
            </q-tooltip>
          </q-icon>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { InvoiceType } from 'src/shared/model/enumerations/invoice-type.model';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { filtersToQueryParams } from 'src/util/filtering';
import { format } from 'src/util/format';

const baseApiUrl = '/api/invoices';
const hasRoleConfig = useAuthenticationStore().hasRoleConfig;
const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const { n, t } = useI18n();
const route = useRoute();
const router = useRouter();
const store = useAuthenticationStore();
const context = route.query.context;

const columns = computed(() => [
  {
    align: 'left',
    field: row => row.buyer?.company?.name,
    filter: {
      field: 'company',
      type: 'enum',
      values: useAuthenticationStore().account.companies.map(company => ({ value: company.id, label: company.name })),
    },
    label: t('afaktoApp.buyer.company'),
    name: 'buyer.company.name',
    sortable: true,
  },
  {
    filter: { type: 'boolean' },
    label: t('afaktoApp.invoice.hasCover'),
    hidden: true,
    name: 'hasCover',
  },
  {
    align: 'left',
    label: t('afaktoApp.invoice.buyerCode'),
    field: row => row.buyer.code,
    filter: {
      field: 'buyer',
      optionLabel: 'code',
      optionValue: 'id',
      type: 'select',
      url: '/api/buyers?sort=code&size=3000',
    },
    hidden: true,
    name: 'buyer.code',
    sortable: true,
  },
  {
    align: 'left',
    label: t('afaktoApp.invoice.buyer'),
    field: row => row.buyer.name,
    filter: {
      field: 'buyer',
      optionLabel: 'name',
      optionValue: 'id',
      type: 'select',
      url: '/api/buyers?sort=name&size=3000',
    },
    name: 'buyer.name',
    sortable: true,
  },
  {
    field: row => row.buyer.excluded,
    label: t('afaktoApp.buyer.excluded'),
    filter: { type: 'boolean' },
    name: 'buyerExcluded',
    sortable: true,
  },
  {
    classes: 'type',
    field: row => t(`afaktoApp.InvoiceType.${row.type}`),
    filter: {
      type: 'enum',
      values: Object.values(InvoiceType).map(type => ({
        label: t(`afaktoApp.InvoiceType.${type}`),
        value: type,
      })),
    },
    headerClasses: 'type',
    label: t('afaktoApp.invoice.type'),
    name: 'type',
    sortable: true,
  },
  {
    align: 'left',
    field: 'invoiceNumber',
    filter: { type: 'text' },
    label: t('afaktoApp.invoice.invoiceNumber'),
    name: 'invoiceNumber',
    sortable: true,
  },
  {
    field: 'date',
    filter: { type: 'date' },
    format: value => value && format(value),
    label: t('afaktoApp.invoice.date'),
    name: 'date',
    sortable: true,
  },
  {
    field: 'dueDate',
    filter: { type: 'date' },
    format: value => value && format(value),
    label: t('afaktoApp.invoice.dueDate'),
    name: 'dueDate',
    sortable: true,
  },
  {
    field: 'currency',
    filter: { type: 'currency' },
    label: t('afaktoApp.invoice.currency'),
    name: 'currency',
    sortable: true,
  },
  {
    classes: row => (row.amount < 0 ? 'text-negative' : 'text-positive'),
    field: 'amount',
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.currency })),
    label: t('afaktoApp.invoice.amount'),
    name: 'amount',
    sortable: true,
  },
  {
    classes: row => (row.balance < 0 ? 'text-negative' : 'text-positive'),
    field: 'balance',
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.currency })),
    label: t('afaktoApp.invoice.balance'),
    name: 'balance',
    sortable: true,
  },
  {
    align: 'center',
    field: 'underFactor',
    filter: { type: 'boolean' },
    label: t('afaktoApp.invoice.underFactor'),
    name: 'isUnderFactor',
    sortable: true,
  },
  {
    classes: row => (row.invoiceFromFactor?.amountFunded < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.invoiceFromFactor?.amountFunded,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.currency })),
    label: t('afaktoApp.invoice.invoiceFromFactor.amountFunded'),
    name: 'invoiceFromFactor_amountFunded',
    sortable: true,
  },
  {
    classes: row => (row.invoiceFromFactor?.amountUnfunded < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.invoiceFromFactor?.amountUnfunded,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.currency })),
    hidden: true,
    label: t('afaktoApp.invoice.invoiceFromFactor.amountUnfunded'),
    name: 'invoiceFromFactor_amountUnfunded',
    sortable: true,
  },
  {
    classes: row => (row.invoiceFromFactor?.amountUnavailable < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.invoiceFromFactor?.amountUnavailable,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.currency })),
    hidden: true,
    label: t('afaktoApp.invoice.invoiceFromFactor.amountUnavailable'),
    name: 'invoiceFromFactor_amountUnavailable',
    sortable: true,
  },
  {
    align: 'left',
    field: 'reconciliationJournal',
    filter: { type: 'text' },
    hidden: context != '_gapCession',
    label: t('afaktoApp.invoice.reconciliationJournal'),
    name: 'reconciliationJournal',
    sortable: true,
  },
  {
    align: 'left',
    field: row => row.gap && t('afaktoApp.invoice.gap'),
    filter: { type: 'boolean' },
    label: t('afaktoApp.invoice.gap'),
    name: 'gap',
    sortable: true,
  },
  {
    align: 'left',
    field: row => (row.gap?.type == null ? '' : t(`afaktoApp.invoice.gapType_values.${row.gap?.type}`)),
    filter: {
      type: 'enum',
      values: ['EXCLUSION', 'OVERDUE', 'RECONCILIATION'].map(type => ({
        label: t(`afaktoApp.invoice.gapType_values.${type}`),
        value: type,
      })),
    },
    hidden: context != '_gapCession',
    label: t('afaktoApp.invoice.gapType'),
    name: 'gapType',
    sortable: true,
  },
  {
    align: 'left',
    field: row => (row.gap?.credit == null ? '' : t(`afaktoApp.invoice.gapCredit_values.${row.gap?.credit}`)),
    filter: { type: 'boolean' },
    hidden: context != '_gapCession',
    label: t('afaktoApp.invoice.gapCredit_values.true'),
    name: 'gapCredit',
    sortable: true,
  },
]);

const visibleColumns = ref([]);
const filters = ref({});
watch(filters, () => onRequest({ pagination: pagination.value }), { deep: true });

const pagination = ref({
  sortBy: route.query.sortBy || 'date',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15,
});

const rows = ref([]);

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  const response = await api.get(baseApiUrl, {
    params: {
      page: page - 1,
      size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
      ...filtersToQueryParams(filters.value),
    },
  });
  rows.value = response.data;

  pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
  router.replace({ query: { page, sortBy, descending, rowsPerPage } });
};
</script>
