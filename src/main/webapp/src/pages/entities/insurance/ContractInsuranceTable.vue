<template>
  <div v-if="rows.length">
    <div v-for="row in rows" :key="row.id" class="row-item">
      <div class="row q-gutter-sm">
        <div v-for="col in columns" :key="col.name" class="col text-center">
          <template v-if="typeof col.field === 'function' ? col.field(row) != null : row[col.field] != null">
            <div>
              <strong>{{ col.label }}</strong>
            </div>
            <span>
              {{ col.format ? col.format(col.field(row), row) : col.field(row) }}
            </span>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { useAuthenticationStore } from 'src/stores/authentication-store';
import { format } from 'src/util/format';
import useNotifications from 'src/util/useNotifications';

const hasRoleConfig = useAuthenticationStore().hasRoleConfig;
const { notifyError } = useNotifications();
const route = useRoute();
const router = useRouter();
const { n, t } = useI18n();
const store = useAuthenticationStore();

const columns = computed(() => [
  {
    align: 'left',
    field: row => row.creditInsurancePolicy?.externalCreditInsurance?.name,
    label: t('afaktoApp.externalCreditInsurance.home.title'),
    name: 'creditInsurancePolicy.externalCreditInsurance.name',
    sortable: true,
  },
  {
    align: 'left',
    field: row => row.creditInsurancePolicy?.policyId,
    label: t('afaktoApp.externalCreditInsurance.policy.policyId'),
    name: 'policyId',
  },
  {
    field: row => row.creditInsurancePolicy?.blindCoverAmount,
    style: 'currency',
    format: (value, row) => {
      if (value == null) return '';

      return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: row.financialInformation?.currency || 'EUR',
        currencyDisplay: 'symbol',
      }).format(value);
    },
    label: t('afaktoApp.externalCreditInsurance.policy.blindCoverAmount'),
    name: 'creditInsurancePolicy_blindCoverAmount',
  },
]);

const rows = ref([]);

const props = defineProps({
  modelValue: Object,
  entity: Object,
});

onMounted(async () => {
  try {
    const response = await api.get('/api/contracts', {
      params: {
        'company.equals': props.entity.company.id,
      },
    });

    rows.value = response.data;
  } catch (error) {
    notifyError(error);
  }
});
</script>
