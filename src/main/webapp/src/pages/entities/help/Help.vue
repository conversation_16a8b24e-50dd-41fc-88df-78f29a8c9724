<template>
  <q-page class="q-pa-md">
    <b-title :model-value="$t('help.title')" />
    <div class="q-gutter-y-md containerWithoutTable" style="max-width: 100%">
      <q-card>
        <q-tabs
          v-model="tab"
          dense
          align="justify"
          class="text-grey"
          active-color="primary"
          indicator-color="action"
          narrow-indicator
          @click="SettingsTab"
        >
          <q-tab name="glossary" label="Glossary" />
          <q-tab name="supportContacts" label="Support Contacts" />
          <q-tab name="referenceLinks" label="Reference Links" />
        </q-tabs>

        <q-separator />

        <q-tab-panels v-model="tab" animated>
          <q-tab-panel name="glossary">
            <div class="text-h6">Glossary</div>
            Lorem ipsum dolor sit amet consectetur adipisicing elit.
          </q-tab-panel>

          <q-tab-panel name="supportContacts">
            <div class="text-h6">Support Contacts</div>
            Lorem ipsum dolor sit amet consectetur adipisicing elit.
          </q-tab-panel>

          <q-tab-panel name="referenceLinks">
            <div class="text-h6">Reference Links</div>
            Lorem ipsum dolor sit amet consectetur adipisicing elit.
          </q-tab-panel>
        </q-tab-panels>
      </q-card>
    </div>
  </q-page>
</template>

<script>
import { api } from 'boot/axios';
import { computed, defineComponent, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { format } from 'src/util/format';

export default defineComponent({
  name: 'PageHelp',

  setup() {
    const route = useRoute();
    const router = useRouter();
    const { t } = useI18n();

    const rows = ref([]);
    const loading = ref(true);

    return {
      tab: ref('glossary'),
    };

    const pagination = ref({
      sortBy: route.query.sortBy || 'id',
      descending: route.query.descending === 'true',
      page: Number.parseInt(route.query.page || 1),
      rowsPerPage: Number.parseInt(route.query.rowsPerPage) || 10,
      rowsNumber: 10,
    });

    const onRequest = async props => {
      const { page, rowsPerPage, sortBy, descending } = props.pagination;

      loading.value = true;

      try {
        const response = await api.get('/api/Help', {
          params: {
            page: page - 1,
            size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
            sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
          },
        });
        pagination.value.rowsNumber = response.headers['x-total-count'];
        rows.value = response.data;
      } finally {
        loading.value = false;
      }

      pagination.value.page = page;
      pagination.value.rowsPerPage = rowsPerPage;
      pagination.value.sortBy = sortBy;
      pagination.value.descending = descending;

      router.replace({ query: { page, sortBy, descending, rowsPerPage } });
    };

    onMounted(() => onRequest({ pagination: pagination.value }));
  },
});
</script>
