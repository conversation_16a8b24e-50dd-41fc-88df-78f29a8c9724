<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.cession.home.title') }} | {{ $t('afaktoApp.cession.detail.title') }}</h1>

      <q-space />

      <!-- Button for downloadCessionFile -->
      <q-btn
        v-for="datastream in cession.datastreams"
        :key="datastream.id"
        class="buttonNeutral"
        :icon="$t(`afaktoApp.DatastreamType.${datastream.type}_icon`)"
        @click="downloadFile(datastream)"
      >
        <q-tooltip>{{ $t(`afaktoApp.DatastreamType.${datastream.type}`) }}</q-tooltip>
      </q-btn>
      <q-btn class="buttonNeutral" icon="close" to=".">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-toolbar>

    <q-toolbar v-if="cession.contract">
      <h2>
        {{ cession.contract.company.name }}
        ({{ cession.contract.financialInformation.currency }}) -
        {{ $d(cession.createdDate, 'short') }}
      </h2>
    </q-toolbar>

    <section v-if="cession.contract" class="row justify-center q-gutter-md q-pa-md">
      <q-card class="situation-card">
        <q-card-section>
          <div>{{ $t('afaktoApp.cession.count') }}</div>
          <h2 class="text-right">{{ $n(cession.count) }}</h2>
          <change-number-display :value="abstract.reduce((sum, type) => sum + type.INSERT.count - type.DELETE.count, 0)" />
        </q-card-section>
      </q-card>

      <q-card class="situation-card">
        <q-card-section>
          <div class="float-right">{{ cession.contract.financialInformation.currency }}</div>
          <div class="card-header">{{ $t('afaktoApp.cession.sum') }}</div>
          <h2 class="text-right">
            {{ $n(cession.sum, 'currency', { currency: cession.contract.financialInformation.currency }) }}
          </h2>
          <change-number-display
            :value="abstract.reduce((sum, type) => sum + type.INSERT.sum - type.DELETE.sum, 0)"
            format="currency"
            :currency="cession.contract.financialInformation.currency"
          />
        </q-card-section>
      </q-card>

      <q-card v-if="cession.contract.cashIn" class="situation-card">
        <q-card-section>
          <div class="card-header">
            <q-icon color="warning" name="bolt" size="xs" />
            {{ $t('afaktoApp.cession.gaps') }}
            -
            {{ $t('afaktoApp.invoice.gapCredit_values.false') }}
            <q-icon
              class="cursor-pointer float-right"
              name="east"
              @click="() => router.push(`/invoices?context=_gapCession&gapCession.equals=${cession.id}&gapCredit.equals=false`)"
            />
          </div>
          <h2 class="text-right">{{ $n(gapsDebitor.reduce((sum, type) => sum + type.count, 0)) }}</h2>
          <h2 class="text-right">
            {{
              $n(
                gapsDebitor.reduce((sum, type) => sum + type.totalAmount, 0),
                'currencyCode',
                { currency: cession.contract.financialInformation.currency },
              )
            }}
          </h2>
        </q-card-section>
      </q-card>

      <q-card v-if="cession.contract.cashIn" class="situation-card">
        <q-card-section>
          <div class="card-header">
            <q-icon color="warning" name="bolt" size="xs" />
            {{ $t('afaktoApp.cession.gaps') }}
            -
            {{ $t('afaktoApp.invoice.gapCredit_values.true') }}
            <q-icon
              class="cursor-pointer float-right"
              name="east"
              @click="() => router.push(`/invoices?context=_gapCession&gapCession.equals=${cession.id}&gapCredit.equals=true`)"
            />
          </div>
          <h2 class="text-right">{{ $n(gapsCreditor.reduce((sum, type) => sum + type.count, 0)) }}</h2>
          <h2 class="text-right">
            {{
              $n(
                gapsCreditor.reduce((sum, type) => sum + type.totalAmount, 0),
                'currencyCode',
                { currency: cession.contract.financialInformation.currency },
              )
            }}
          </h2>
        </q-card-section>
      </q-card>
    </section>

    <section v-if="cession.contract" class="row justify-center q-pa-md">
      <q-card>
        <!-- Display an abstract of amounts for the selected, paid, current invoices and gaps -->
        <q-table :columns="abstractColumns" hide-pagination row-key="type" :rows="abstract">
          <template #header>
            <q-tr>
              <q-th></q-th>
              <q-th colspan="3">
                <q-icon color="positive" left name="add_circle" size="xs" />
                {{ $t('afaktoApp.cession.sold') }}
              </q-th>
              <q-th colspan="3">
                <q-icon color="info" left name="pending" size="xs" />
                {{ $t('afaktoApp.cession.current') }}
              </q-th>
              <q-th colspan="3">
                <q-icon color="negative" left name="do_not_disturb_on" size="xs" />
                {{ $t('afaktoApp.cession.paid') }}
              </q-th>
              <q-th v-if="cession.contract.cashIn" colspan="3">
                <q-icon color="warning" name="bolt" size="xs" />
                {{ $t('afaktoApp.cession.gaps') }}
                -
                {{ $t('afaktoApp.invoice.gapCredit_values.false') }}
              </q-th>
              <q-th v-if="cession.contract.cashIn" colspan="3">
                <q-icon color="warning" name="bolt" size="xs" />
                {{ $t('afaktoApp.cession.gaps') }}
                -
                {{ $t('afaktoApp.invoice.gapCredit_values.true') }}
              </q-th>
            </q-tr>
          </template>
        </q-table>
      </q-card>
    </section>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      class="q-pt-md"
      :columns="columns"
      row-key="id"
      :rows="rows"
      :rows-per-page-options="[0]"
      @row-click="(_, { id }) => router.push(`/invoices/${id}`)"
      @request="onRequest"
    />

    <entity-meta :entity="cession" />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { exportFile } from 'quasar';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import EntityMeta from 'pages/subcomponents/EntityMeta.vue';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { format } from 'src/util/format';

const baseApiUrl = '/api/cessions';
const datastreamApiUrl = '/api/datastreams';
const router = useRouter();
const route = useRoute();
const { n, t } = useI18n();
const store = useAuthenticationStore();

const cession = ref({});

const abstract = computed(() =>
  Object.entries(cession.value.metadata).map(([type, item]) => ({
    type,
    count: cession.value.count,
    sum: cession.value.sum,
    INSERT: { count: item.INSERT[0], sum: item.INSERT[1] },
    UPDATE: { count: item.UPDATE[0], sum: item.UPDATE[1] },
    DELETE: { count: item.DELETE[0], sum: item.DELETE[1] },
  })),
);

const abstractColumns = computed(() => [
  { align: 'left', field: 'type', format: v => t(`afaktoApp.InvoiceType.${v}`) },
  { classes: 'table-separator' },
  { field: row => row.INSERT.count, format: v => n(v) },
  { field: row => row.INSERT.sum, format: v => n(v, 'currencyCode', { currency: cession.value?.contract?.financialInformation.currency }) },
  { classes: 'table-separator' },
  { field: row => row.UPDATE.count, format: v => n(v) },
  { field: row => row.UPDATE.sum, format: v => n(v, 'currencyCode', { currency: cession.value?.contract?.financialInformation.currency }) },
  { classes: 'table-separator' },
  { field: row => row.DELETE.count, format: v => n(v) },
  { field: row => row.DELETE.sum, format: v => n(v, 'currencyCode', { currency: cession.value?.contract?.financialInformation.currency }) },
  { classes: 'table-separator' },
  // Four more columns for gaps count and amount
  {
    classes: () => (cession.value.contract.cashIn ? '' : 'hidden'),
    field: row => gapsDebitor.value.filter(gap => gap.type === row.type).reduce((count, gap) => count + gap.count, 0),
    format: v => n(v),
  },
  {
    classes: () => (cession.value.contract.cashIn ? '' : 'hidden'),
    field: row => gapsDebitor.value.filter(gap => gap.type === row.type).reduce((sum, gap) => sum + gap.totalAmount, 0),
    format: v => n(v, 'currencyCode', { currency: cession.value?.contract?.financialInformation.currency }),
  },

  {
    classes: () => (cession.value.contract.cashIn ? '' : 'hidden'),
    field: row => gapsCreditor.value.filter(gap => gap.type === row.type).reduce((count, gap) => count + gap.count, 0),
    format: v => n(v),
  },
  {
    classes: () => (cession.value.contract.cashIn ? '' : 'hidden'),
    field: row => gapsCreditor.value.filter(gap => gap.type === row.type).reduce((sum, gap) => sum + gap.totalAmount, 0),
    format: v => n(v, 'currencyCode', { currency: cession.value?.contract?.financialInformation.currency }),
  },
]);

const columns = computed(() => [
  {
    align: 'left',
    field: row => row.buyer?.code,
    label: t('afaktoApp.invoice.buyerCode'),
    name: 'buyer.code',
    sortable: true,
  },
  {
    align: 'left',
    field: row => row.buyer?.name,
    label: t('afaktoApp.invoice.buyer'),
    name: 'buyer.name',
    sortable: true,
  },
  {
    field: 'type',
    format: v => t(`afaktoApp.InvoiceType.${v}`),
    label: t('afaktoApp.invoice.type'),
    name: 'type',
    sortable: true,
  },
  {
    align: 'left',
    field: 'invoiceNumber',
    label: t('afaktoApp.invoice.invoiceNumber'),
    name: 'invoiceNumber',
    sortable: true,
  },
  {
    field: 'date',
    format: v => v && format(v),
    label: t('afaktoApp.invoice.date'),
    name: 'date',
    sortable: true,
  },
  {
    field: 'dueDate',
    format: v => v && format(v),
    label: t('afaktoApp.invoice.dueDate'),
    name: 'dueDate',
    sortable: true,
  },
  {
    classes: props => (props.amount < 0 ? 'text-negative' : 'text-positive'),
    field: 'amount',
    format: v => n(v, 'currency', { currency: cession.value?.contract?.financialInformation.currency }),
    label: t('afaktoApp.invoice.amount'),
    name: 'amount',
    sortable: true,
  },
  {
    classes: props => (props.balance < 0 ? 'text-negative' : 'text-positive'),
    field: 'balance',
    format: v => n(v, 'currency', { currency: cession.value?.contract?.financialInformation.currency }),
    label: t('afaktoApp.invoice.balance'),
    name: 'balance',
    sortable: true,
  },
  {
    classes: props => (props.invoiceFromFactor?.amountFunded < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.invoiceFromFactor?.amountFunded,
    format: v => (v == null ? '' : n(v, 'currency', { currency: cession.value?.contract?.financialInformation.currency })),
    label: t('afaktoApp.invoice.invoiceFromFactor.amountFunded'),
    name: 'invoiceFromFactor.amountFunded',
    sortable: true,
  },
]);

const pagination = ref({
  sortBy: route.query.sortBy || 'date',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15,
});

const rows = ref([]);
const gapsDebitor = ref([]);
const gapsCreditor = ref([]);

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  const response = await api.get('/api/invoices', {
    params: {
      page: page - 1,
      size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
      'cession.equals': route.params.id,
    },
  });
  rows.value = response.data;

  pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
  router.replace({ query: { page, sortBy, descending, rowsPerPage } });
};

const downloadFile = async datastream => {
  try {
    const response = await api.get(`${datastreamApiUrl}/${datastream.id}/download`, {
      responseType: 'blob',
    });
    exportFile(datastream.name, response.data);
  } catch (error) {
    notifyError(error);
  }
};

onMounted(async () => {
  if (!route.params.id) return;

  cession.value = (await api.get(`${baseApiUrl}/${route.params.id}`)).data;

  if (cession.value.contract.cashIn) {
    const responseDebitor = await api.get('/api/invoices/byType', {
      params: {
        'gapCession.equals': route.params.id,
        'gapCredit.equals': false,
      },
    });
    gapsDebitor.value = responseDebitor.data;

    const responseCreditor = await api.get('/api/invoices/byType', {
      params: {
        'gapCession.equals': route.params.id,
        'gapCredit.equals': true,
      },
    });
    gapsCreditor.value = responseCreditor.data;
  }

  onRequest({ pagination: pagination.value });
});
</script>
