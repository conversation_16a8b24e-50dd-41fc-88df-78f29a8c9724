<template>
  <q-btn-dropdown
    class="q-px-md"
    :color="rows?.length ? 'warning' : 'neutralHighest'"
    dropdown-icon="o_notifications"
    fab-mini
    flat
    style="border: none; box-shadow: none"
    @click="isDialogOpen = true"
  />

  <q-dialog maximized v-model="isDialogOpen" position="right">
    <q-card style="height: 100%; border-radius: 0px; width: 600px">
      <q-timeline style="margin-top: 0px; height: 100%; width: 100%">
        <q-card-section style="background-color: #38b29c">
          <div class="text-h6 text-white">Notifications</div>
        </q-card-section>

        <q-card-section>
          <q-tabs v-model="tab" dense class="text-black" active-color="primary" indicator-color="primary" align="justify" narrow-indicator>
            <q-tab name="new" label="New" />
            <q-tab name="all" label="All" />
          </q-tabs>
          <q-list v-if="tab === 'new'">
            <q-item v-for="comment in rows" :key="comment.id" clickable v-ripple :to="getUrl(comment)" @click="closeComment(comment)">
              <q-item-section avatar>
                <q-icon :name="ICONS[comment.relatedEntityType]" />
              </q-item-section>

              <q-timeline-entry :subtitle="$d(comment.createdDate, 'long')" :body="comment.text">
                <q-item-label caption> {{ $t('global.field.by') }} 👤 {{ comment.createdBy }} </q-item-label>
              </q-timeline-entry>

              <q-item-section class="q-ml-auto" side>
                <q-btn dense flat icon="close" @click.prevent="closeComment(comment, $event)" />
              </q-item-section>
            </q-item>
          </q-list>

          <q-list v-if="tab === 'all'">
            <q-item v-for="comment in allRows" :key="comment.id" clickable v-ripple :to="getUrl(comment)">
              <q-item-section avatar>
                <q-icon :name="ICONS[comment.relatedEntityType]" />
              </q-item-section>

              <q-timeline-entry :subtitle="$d(comment.createdDate, 'long')" :body="comment.text">
                <q-item-label caption> {{ $t('global.field.by') }} 👤 {{ comment.createdBy }} </q-item-label>
              </q-timeline-entry>
              <q-card-actions align="center" class="hidden">
                <q-btn flat label="Fermer" color="primary" @click="isDialogOpen = false" />
              </q-card-actions>
            </q-item>
          </q-list>
        </q-card-section>

        <q-card-actions align="center">
          <q-btn flat label="Fermer" color="primary" @click="isDialogOpen = false" />
        </q-card-actions>

        <q-inner-loading :showing="loading">
          <q-spinner-gears color="primary" size="10em" />
        </q-inner-loading>
      </q-timeline>
    </q-card>
  </q-dialog>
</template>

<script setup>
import pluralize from 'pluralize';

import { api } from 'boot/axios';
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { ICONS } from 'src/constants/icons';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import useNotifications from 'src/util/useNotifications.js';

const loading = ref(false);
const route = useRoute();
const store = useAuthenticationStore();
const tab = ref('new');
const { notifyError } = useNotifications();

const baseApiUrl = '/api/comments';
const pagination = ref({
  sortBy: route.query.sortBy || 'createdDate',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: Number.parseInt(route.query.rowsPerPage) || 4,
});

const isDialogOpen = ref(false);
const rows = ref([]);
const allRows = ref([]);

const getUrl = comment => {
  var type = pluralize(comment.relatedEntityType);
  var id = comment.relatedEntityId;
  return `/${type}/${id}`;
};

const closeComment = async (comment, event) => {
  try {
    await api.delete(`${baseApiUrl}/${comment.id}/comment-users/me`);
  } catch (error) {
    notifyError(error);
  }

  rows.value = rows.value.filter(c => c.id !== comment.id);

  if (!event && route.path.startsWith('/' + comment.relatedEntityType))
    // Move to the target element
    document.location.href = getUrl(comment);
};

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  try {
    const response = await api.get(baseApiUrl, {
      params: {
        page: page - 1,
        size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
        sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
        'user.equals': store._account.id,
      },
    });
    rows.value = response.data;

    pagination.value.rowsNumber = response.headers['x-total-count'];
  } catch (error) {
    notifyError(error);
  }

  pagination.value.page = page;
  pagination.value.rowsPerPage = rowsPerPage;
  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;
};

const fetchAllComments = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;
  loading.value = true;

  try {
    const response = await api.get(baseApiUrl, {
      params: {
        page: page - 1,
        size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
        sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
      },
    });
    allRows.value = response.data;
  } catch (error) {
    notifyError(error);
  } finally {
    loading.value = false;
  }
};

watch(
  tab,
  newTab => {
    if (newTab === 'new') {
      onRequest({ pagination: pagination.value });
    } else if (newTab === 'all') {
      fetchAllComments({ pagination: pagination.value });
    }
  },
  { immediate: true }, // This makes it run immediately on component mount
);
</script>

<style>
.q-timeline {
  overflow: visible !important;
}

.body--dark .q-timeline__content {
  color: white;
}

.q-card--dark .q-tab {
  color: white;
}
</style>
