<template>
  <q-page-sticky v-if="entity?.id" position="bottom-left">
    <entity-meta-dates :entity="entity" />
  </q-page-sticky>

  <q-page-sticky v-if="entity?.id" class="comments">
    <q-card v-if="hasRoleWriter">
      <q-input v-model="text" borderless :label="$t('afaktoApp.comment.text_helper')" rows="2" type="textarea">
        <template #append>
          <q-btn color="primary" icon="o_send" type="submit" @click="comment" />
        </template>
      </q-input>
    </q-card>
    <q-card v-for="comment in comments" :key="comment.id">
      <q-card-section style="white-space: pre-line">{{ comment.text }}</q-card-section>
      <div class="created">
        {{ $d(comment.createdDate, 'long') }}
        {{ $t('global.field.by') }}
        👤 {{ comment.createdBy }}
      </div>
    </q-card>
  </q-page-sticky>
</template>

<script setup>
import pluralize from 'pluralize';

import { api } from 'boot/axios';
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import EntityMetaDates from 'pages/subcomponents/EntityMetaDates.vue';
import { useAuthenticationStore } from 'src/stores/authentication-store';

const route = useRoute();

const baseApiUrl = '/api/comments';
const comments = ref([]);
const entityType = pluralize.singular(route.path.split('/')[1]);
const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const text = ref('');

const props = defineProps({
  entity: {
    type: Object,
    required: true,
  },
});

const loadComments = async () => {
  comments.value = (
    await api.get(baseApiUrl, {
      params: {
        'relatedEntityId.equals': props.entity.id,
        'relatedEntityType.equals': entityType,
      },
    })
  ).data;
};

const comment = async () => {
  if (!text.value) return;

  const newComment = (
    await api.post(baseApiUrl, {
      text: text.value,
      relatedEntityId: props.entity.id,
      relatedEntityType: entityType,
    })
  ).data;
  comments.value.unshift(newComment);
  text.value = '';
};

watch(
  () => props.entity?.id,
  newId => {
    if (newId) {
      loadComments();
    } else {
      comments.value = []; // Clear comments when no id
    }
  },
  { immediate: true },
);
</script>
