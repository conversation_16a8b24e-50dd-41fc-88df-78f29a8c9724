<template>
  <section class="topBuyers col-md col-sm-12 q-mx-auto">
    <q-card class="dashboard-card row column q-pb-md">
      <q-tooltip class="bg-primary">{{ $t('afaktoApp.dashboard.topBuyersTooltip') }}</q-tooltip>
      <q-card-section class="q-px-lg q-pt-lg q-pb-none">
        <div class="flex justify-between q-pb-md items-baseline">
          <div class="outstanding-tab-header">
            <h2 class="no-margin">{{ $t('afaktoApp.dashboard.outstandingAmount') }}</h2>
            <q-icon class="cursor-pointer" name="east" @click="$router.push('/invoices?context=_outstanding&balance.notEquals=0&')" />
          </div>
          <div class="row q-gutter-sm items-baseline">
            <p v-if="totalContractSituation[selectedCurrency]" class="no-margin text-neutralHigher" style="font-size: 18px">
              {{ $n(((amountOutstanding[selectedCurrency]?.second || 0) / totalContractSituation[selectedCurrency]) * 100, 'percent') }}%
            </p>
            <h2 v-if="amountOutstanding[selectedCurrency]">
              {{ $n(amountOutstanding[selectedCurrency].second, 'compact', { currency: selectedCurrency, signDisplay: 'negative' }) }}
            </h2>
            <h2 v-else>
              {{ $n(0, 'compact', { currency: selectedCurrency, signDisplay: 'negative' }) }}
            </h2>
          </div>
        </div>
        <q-list dense>
          <p class="q-py-sm q-px-md no-margin text-bold" style="font-size: 12px">{{ $t('afaktoApp.buyer.home.title') }}</p>
          <q-item v-for="data in topBuyers[selectedCurrency]" :key="data.buyer" :to="`/buyers/${data.id}`">
            <q-item-section style="font-size: 13px">{{ data.name.toUpperCase() }}</q-item-section>
            <q-item-section class="amount" side>
              {{ $n(data.amount, 'currency', { currency: data.currency }) }}
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>
      <div class="flex justify-end q-py-sm q-mt-auto" style="padding-inline: 40px">
        <p class="q-mr-md q-my-none">{{ $t('afaktoApp.dashboard.topBuyers') }}</p>
        <p class="text-bold q-my-none">
          {{ $n(topOutstandingTotal, 'compact', { currency: selectedCurrency, signDisplay: 'negative' }) }}
        </p>
      </div>
    </q-card>
  </section>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, onMounted, ref } from 'vue';

import useNotifications from 'src/util/useNotifications';

const { notifyError } = useNotifications();

const topBuyers = ref({});
const amountOutstanding = ref({});
const contractSituation = ref({});

const props = defineProps({
  companies: {
    type: Array,
    default: () => [],
  },
  selectedCurrency: {
    type: String,
  },
});

const topOutstandingTotal = computed(() => {
  return topBuyers.value[props.selectedCurrency]?.reduce((sum, buyer) => sum + buyer.amount, 0) || 0;
});

const totalContractSituation = computed(() => {
  const result = {};

  Object.entries(contractSituation.value).forEach(([currency, values]) => {
    result[currency] = Object.values(values).reduce((sum, amount) => sum + amount, 0);
  });

  return result;
});

onMounted(async () => {
  try {
    const [topOutstandingRes, amountOutstandingRes, contractSituationRes] = await Promise.all([
      api.get('/api/dashboard/topBuyers', {
        params: { companies: props.companies.map(company => company.id).join(',') },
      }),
      api.get('/api/dashboard/amountOutstanding', {
        params: { companies: props.companies.map(company => company.id).join(',') },
      }),
      api.get('/api/dashboard/contractSituation', {
        params: { companies: props.companies.map(company => company.id).join(',') },
      }),
    ]);
    // Group by currency
    topBuyers.value = Object.values(topOutstandingRes.data).reduce((acc, curr) => {
      const currency = curr.currency;
      if (!acc[currency]) {
        acc[currency] = [];
      }
      acc[currency].push(curr);
      return acc;
    }, {});
    amountOutstanding.value = amountOutstandingRes.data;
    contractSituation.value = contractSituationRes.data;
  } catch (error) {
    notifyError(error);
  }
});
</script>
