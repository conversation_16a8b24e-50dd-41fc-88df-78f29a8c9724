<template>
  <q-page :key="componentKey" class="q-col-gutter-md">
    <q-toolbar>
      <q-select
        v-if="options?.length > 1"
        borderless
        class="new-filter"
        :class="{ 'new-filter-active': companies.length > 0 }"
        dense
        v-model="companies"
        :label="$t('afaktoApp.dashboard.companies')"
        multiple
        option-label="name"
        :options="options"
        options-selected-class="text-bold"
        stack-label
      />
      <b-currency
        v-if="!getReferenceCurrency"
        borderless
        class="new-filter"
        :class="{ 'new-filter-active': selectedCurrency }"
        dense
        hide-bottom-space
        v-model="selectedCurrency"
        :label="$t('afaktoApp.dashboard.currency')"
        options-selected-class="text-bold"
        stack-label
      />

      <q-space />

      <q-btn class="buttonBrand" icon="o_done_all" :label="$t('afaktoApp.cession.home.createLabel')" to="cessions/new" />
    </q-toolbar>

    <section class="q-col-gutter-md row">
      <ContractSituation :companies="companies" :selectedCurrency="selectedCurrency" />
      <FactorDebt :companies="companies" :selectedCurrency="selectedCurrency" />
    </section>

    <section class="q-col-gutter-md row">
      <SituationBar :companies="companies" :selectedCurrency="selectedCurrency" />
    </section>

    <section class="q-col-gutter-md row">
      <TopBuyers :companies="companies" :selectedCurrency="selectedCurrency" />
      <TopUnavailables :companies="companies" :selectedCurrency="selectedCurrency" />
    </section>
  </q-page>
</template>

<script setup>
import { computed, ref, watch } from 'vue';

import { useAuthenticationStore } from 'src/stores/authentication-store';

import ContractSituation from './dashboard/ContractSituation.vue';
import FactorDebt from './dashboard/FactorDebt.vue';
import SituationBar from './dashboard/SituationBar.vue';
import TopBuyers from './dashboard/TopBuyers.vue';
import TopUnavailables from './dashboard/TopUnavailables.vue';

const companies = ref([]);
const componentKey = ref(0);
const store = useAuthenticationStore();
const options = ref(store.account?.companies);
const selectedCurrency = ref(store._account?.preferences?.REFERENCE_CURRENCY || localStorage.getItem('selectedCurrency') || 'EUR');

watch(selectedCurrency, newValue => localStorage.setItem('selectedCurrency', newValue));

// Increment componentKey when companies changes
watch(companies, () => componentKey.value++);

const getReferenceCurrency = computed(() => store._account?.preferences?.REFERENCE_CURRENCY);
</script>
