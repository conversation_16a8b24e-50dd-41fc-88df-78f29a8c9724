import { route } from 'quasar/wrappers';
import { createRouter, createWebHistory } from 'vue-router';

import { entityRoutes } from 'src/router/entityRoutes';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { beforeEachAuth } from 'src/util/authentication';
import { beforeUnloadRoute } from 'src/util/formBeforeUnload';

const routes = [
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: '/', component: () => import('pages/IndexPage.vue') },
      {
        path: '/configuration',
        component: () => import('pages/Configuration.vue'),
        beforeEnter: () => {
          const store = useAuthenticationStore();
          return store.hasRoleAdmin;
        },
      },
      {
        path: '/health',
        component: () => import('pages/Health.vue'),
        beforeEnter: () => {
          const store = useAuthenticationStore();
          return store.hasRoleAdmin;
        },
      },
      {
        path: '/metrics',
        component: () => import('pages/Metrics.vue'),
        beforeEnter: () => {
          const store = useAuthenticationStore();
          return store.hasRoleAdmin;
        },
      },
      {
        path: '/logs',
        component: () => import('pages/Logs.vue'),
        beforeEnter: () => {
          const store = useAuthenticationStore();
          return store.hasRoleAdmin;
        },
      },
      {
        path: '/docs',
        component: () => import('pages/Docs.vue'),
        beforeEnter: () => {
          const store = useAuthenticationStore();
          return store.hasRoleAdmin;
        },
      },
      {
        path: '/ex',
        component: () => import('components/Examples.vue'),
      },
      {
        path: '/test',
        component: () => import('pages/TestComponents.vue'),
      },
      ...entityRoutes,
    ],
  },
  {
    path: '/error-401',
    meta: { public: true },
    component: () => import('pages/Error401.vue'),
  },
  {
    path: '/goodbye',
    meta: { public: true },
    component: () => import('pages/Goodbye.vue'),
  },
  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    meta: { public: true },
    component: () => import('pages/Error404.vue'),
  },
];

export default route(function (/* { store } */) {
  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createWebHistory(process.env.VUE_ROUTER_BASE),
  });

  // To confirm navigation away from dirty forms
  Router.beforeResolve(beforeUnloadRoute);

  // To check if user is authenticated
  Router.beforeEach(beforeEachAuth);

  return Router;
});
