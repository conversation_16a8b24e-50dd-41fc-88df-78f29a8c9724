/*
 * THIS FILE IS HIGHLY MODIFIED BY THE BLUEPRINT
 * DO NOT EDIT.
 *
 */
import { bankTransactionRoutes } from './entities/bankTransactionRoutes';
import { buyerRoutes } from './entities/buyerRoutes';
import { categoryToAccountRoutes } from './entities/categoryToAccountRoutes';
import { cessionRoutes } from './entities/cessionRoutes';
import { companyRoutes } from './entities/companyRoutes';
import { contractRoutes } from './entities/contractRoutes';
import { creditLimitRequestRoutes } from './entities/creditLimitRequestRoutes';
import { creditLimitRoutes } from './entities/creditLimitRoutes';
import { dashboardRoutes } from './entities/dashboardRoutes';
import { datastreamRoutes } from './entities/datastreamRoutes';
import { externalCreditInsurance } from './entities/externalCreditInsurance';
import { factorInstitutionRoutes } from './entities/factorInstitutionRoutes';
import { helpRoutes } from './entities/helpRoutes';
import { invoiceRoutes } from './entities/invoiceRoutes';
import { userRoutes } from './entities/userRoutes';

export const entityRoutes = [
  ...categoryToAccountRoutes,
  ...dashboardRoutes,
  ...buyerRoutes,
  ...cessionRoutes,
  ...bankTransactionRoutes,
  ...companyRoutes,
  ...factorInstitutionRoutes,
  ...datastreamRoutes,
  ...contractRoutes,
  ...invoiceRoutes,
  ...creditLimitRoutes,
  ...creditLimitRequestRoutes,
  ...externalCreditInsurance,
  ...helpRoutes,
  ...userRoutes,
  ...[],
];
