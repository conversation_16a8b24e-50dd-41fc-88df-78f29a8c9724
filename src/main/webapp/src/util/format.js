import { formatDistanceStrict as formatDistanceStrict_, format as format_ } from 'date-fns';

import { importLocale } from '../constants/i18nConstants';

export function format(date, formatStr = 'dd/MM/yyyy') {
  if (!date) return '';
  return format_(date, formatStr, { locale: importLocale().default });
}

export function formatDistanceStrict(date, baseDate = new Date(0)) {
  return formatDistanceStrict_(date, baseDate, { locale: importLocale().default });
}
