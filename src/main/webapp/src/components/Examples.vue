<template>
  <div>
    <div>String s{{ stringValue }}</div>
    <div>Int {{ intValue }}</div>
    <div>Dec {{ decValue }}</div>
    <b-input ref="intRef" v-model="intValue" type="number" label="Int" :rules="ageRules" />
    <b-input ref="decRef" v-model="decValue" type="number" label="Dec" :rules="ageRules" />
    <b-input ref="stringRef" v-model="stringValue" type="text" label="String" :rules="ageRules" />
    <b-input v-model="stringDate" type="date" label="String" />

    <b-button-bar>
      <b-button label="Label Primary" look="primary" :on-click="submit" />
      <b-button label="Label Secondary" type="secondary" :on-click="submit" />
    </b-button-bar>
    <p>Clicks on todos: {{ clickCount }}</p>
  </div>
</template>

<script lang="ts">
import { date, useQuasar } from 'quasar';
import { defineComponent, reactive, ref, toRefs } from 'vue';

function useClickCount() {
  const clickCount = ref(0);
  function increment() {
    clickCount.value += 1;
    return clickCount.value;
  }
  return { clickCount, increment };
}

function fakeMe(done: () => void) {
  setTimeout(() => {
    done();
  }, 1000);
}

export default defineComponent({
  name: 'ExampleComponent',
  props: {},
  setup() {
    const newDate = Date.parse('2022-06-21T17:38:23+02:00');
    // const formattedString = date.formatDate(newDate, 'YYYY-MM-DDTHH:mm:ss.SSSZ')
    const formattedString = date.formatDate(newDate, 'YYYY-MM-DD');

    const $q = useQuasar();
    const inputs = () => {
      const state = reactive({
        intValue: 10,
        decValue: 10.5,
        stringValue: 'He',
        stringDate: formattedString,
        date: formattedString,
        intValueRef: 10,
        decValueRef: 10.5,
        stringValueRef: 'He',
        otherValue: 'He' as string | number,
      });
      return { ...toRefs(state) };
    };

    const submit = () => {
      $q.notify({
        icon: 'done',
        color: 'positive',
        message: 'Submitted',
      });
    };

    return {
      fakeMe,
      submit,
      ...useClickCount(),
      ...inputs(),
      ageRules: [
        (val: number) => val !== null || 'Please type your age',
        (val: number) => (val > 0 && val < 100) || 'Please type a real age',
      ],
    };
  },
});
</script>
