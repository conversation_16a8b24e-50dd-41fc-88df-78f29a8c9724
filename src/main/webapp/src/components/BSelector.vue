<template>
  <q-select
    v-model="theModel"
    :filled="filled"
    :label="label"
    :loading="loading"
    map-options
    :multiple="multiple"
    :options="filteredOptions.length ? filteredOptions : options"
    options-selected-class="text-bold"
    :option-label="optionLabel"
    :option-value="optionValue"
    :rules="rules"
    use-input
    @filter="filterFn"
  >
    <template #input="scope">
      <q-input v-bind="scope" @input="onInput" debounce="0" />
    </template>
    <template v-if="slots.option" v-slot:option="scope">
      <slot name="option" v-bind="scope" />
    </template>
    <template v-if="slots.prepend" #prepend>
      <slot name="prepend" />
    </template>
  </q-select>
</template>

<script lang="ts" setup>
import { api } from 'boot/axios';
import { useSlots } from 'vue';
import { PropType, computed, onMounted, ref } from 'vue';

import useNotifications from 'src/util/useNotifications';

const slots = useSlots();

const props = defineProps({
  filled: {
    type: Boolean,
    default: true,
  },
  label: {
    type: String,
    required: false,
    default: '',
  },
  modelValue: {
    type: [String, Object], // Accepts both
    default: () => null,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  optionLabel: {
    type: [String, Function] as PropType<string | ((option: any) => string)>,
    required: true,
  },
  optionValue: {
    type: [String, Function] as PropType<string | ((option: any) => any)>,
    default: (option: string | Function) => option,
  },
  optionsPreload: {
    type: Boolean,
    default: false,
  },
  rules: {
    type: Object as PropType<any[]>,
    default: null,
  },
  url: {
    type: String,
    default: '',
  },
  staticOptions: {
    type: Array as PropType<any[]>,
    default: null,
  },
});

const emit = defineEmits(['update:modelValue']);
const { notifyError } = useNotifications();

const theModel = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
});

const loading = ref();
const allOptions = ref([]);
const filteredOptions = ref([]);

const options = computed(() => {
  return props.staticOptions ?? allOptions.value;
});

const loadAllOptions = async () => {
  loading.value = true;
  try {
    if (props.staticOptions) {
      allOptions.value = props.staticOptions;
    } else if (props.url) {
      allOptions.value = (await api.get(props.url)).data ?? [];
    }
  } catch (error: any) {
    notifyError(error);
  } finally {
    loading.value = false;
  }
};

const filterFn = async (val: string, update: Function) => {
  if (!allOptions.value.length && !props.staticOptions) {
    await loadAllOptions();
  }

  const sourceOptions = props.staticOptions ?? allOptions.value;

  const result = sourceOptions.filter(option => {
    const label = typeof props.optionLabel === 'function' ? props.optionLabel(option) : option[props.optionLabel];
    return label?.toLowerCase()?.includes(val.toLowerCase());
  });

  update(() => {
    filteredOptions.value = result;
  });
};

onMounted(async () => {
  if (props.optionsPreload) loadAllOptions();
});
</script>
