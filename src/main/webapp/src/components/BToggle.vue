<template>
  <q-toggle v-bind="$attrs" :model-value="internalValue" @update:model-value="updateValue" />
</template>

<script>
export default {
  name: 'BToggle',
  props: {
    modelValue: {
      required: true,
    },
  },
  computed: {
    internalValue() {
      return this.modelValue ?? false;
    },
  },
  methods: {
    updateValue(val) {
      this.$emit('update:model-value', val);
    },
  },
};
</script>
