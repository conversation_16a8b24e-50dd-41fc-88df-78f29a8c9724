package com.afakto.batch;

import static java.util.Map.entry;
import static org.springframework.util.ObjectUtils.isEmpty;

import java.util.Arrays;
import java.util.Map;
import java.util.NoSuchElementException;

import org.springframework.batch.core.ItemWriteListener;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.data.builder.RepositoryItemWriterBuilder;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.batch.item.file.LineMapper;
import org.springframework.batch.item.file.builder.FlatFileItemReaderBuilder;
import org.springframework.batch.item.file.mapping.BeanWrapperFieldSetMapper;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

import com.afakto.batch.common.BatchErrorHandlers;
import com.afakto.batch.common.LineWrapper;
import com.afakto.domain.Address;
import com.afakto.domain.Buyer;
import com.afakto.domain.Contact;
import com.afakto.domain.Datastream;
import com.afakto.repository.BuyerRepository;
import com.afakto.repository.CompanyRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class BuyerImport {
    private final BuyerRepository buyerRepository;
    private final CompanyRepository companyRepository;
    private final CustomConversionService customConversionService;

    private final JobRepository jobRepository;
    private final PlatformTransactionManager transactionManager;

    private DelimitedLineTokenizer lineTokenizer;

    private static final Map<String, String> HEADER_TO_FIELD_MAPPING = Map.ofEntries(
            entry("ADDRESS_CITY", "address.city"),
            entry("ADDRESS_COUNTRY", "address.country"),
            entry("ADDRESS_POSTAL_CODE", "address.postalCode"),
            entry("ADDRESS_STATE", "address.stateProvince"),
            entry("ADDRESS_STREET_NAME", "address.streetName"),
            entry("ADDRESS_STREET_NUMBER", "address.streetNumber"),
            entry("BUYER_CODE", "code"),
            entry("BUYER_NAME", "name"),
            entry("COMPANY", "company.code"),
            entry("COMPANY_CODE", "company.code"),
            entry("CONTACT_EMAIL", "contact.email"),
            entry("CONTACT_NAME", "contact.name"),
            entry("CONTACT_PHONE", "contact.phone"),
            entry("COUNTRY_CODE", "address.country"),
            entry("ID_NUMBER", "number"),
            entry("ID_TYPE", "number_type"),
            entry("PAYMENT_TERMS", "paymentTerms.numberOfDays"));

    public Step createStep(Datastream datastream, Resource resource) {
        return new StepBuilder("importBuyerStep", jobRepository)
                .<LineWrapper<Buyer>, Buyer>chunk(25, transactionManager)
                .reader(new FlatFileItemReaderBuilder<LineWrapper<Buyer>>()
                        .beanMapperStrict(false)
                        .lineMapper(createLineMapper())
                        .linesToSkip(1)
                        .skippedLinesCallback(this::setupHeader)
                        .name("buyerReader")
                        .resource(resource)
                        .strict(false)
                        .build())
                .processor(lineWrapper -> processLine(datastream, lineWrapper))
                .writer(new RepositoryItemWriterBuilder<Buyer>()
                        .repository(buyerRepository)
                        .build())
                .faultTolerant()
                .skip(FlatFileParseException.class)
                .skip(NoSuchElementException.class)
                .skipLimit(1000)
                .listener(BatchErrorHandlers.createReadErrorListener(datastream))
                .listener(BatchErrorHandlers.createProcessErrorListener(datastream))
                .listener(new ItemWriteListener<Buyer>() {
                    @Override
                    public void beforeWrite(Chunk<? extends Buyer> items) {
                        items.forEach(item -> {
                            // Count inserts/updates before writing to database
                            // This avoids double-counting during chunk replays
                            if (item.getId() == null) datastream.incrementInserts();
                            else datastream.incrementUpdates();
                        });
                    }
                })
                .build();
    }

    private LineMapper<LineWrapper<Buyer>> createLineMapper() {
        lineTokenizer = new DelimitedLineTokenizer();
        lineTokenizer.setStrict(false);

        BeanWrapperFieldSetMapper<Buyer> fieldSetMapper = new BeanWrapperFieldSetMapper<>();
        fieldSetMapper.setConversionService(customConversionService.getConversionService());
        fieldSetMapper.setStrict(false);
        fieldSetMapper.setTargetType(Buyer.class);

        DefaultLineMapper<Buyer> defaultLineMapper = new DefaultLineMapper<>();
        defaultLineMapper.setFieldSetMapper(fieldSetMapper);
        defaultLineMapper.setLineTokenizer(lineTokenizer);

        // Simple wrapper using method reference
        return (line, lineNumber) -> new LineWrapper<>(lineNumber, line, defaultLineMapper.mapLine(line, lineNumber));
    }

    /**
     * Process the skipped line to map the header to the field name.
     *
     * @param headerLine the header line
     */
    private void setupHeader(String headerLine) {
        // Determine and set the delimiter
        String delimiter = headerLine.contains(";") ? ";" : ",";
        lineTokenizer.setDelimiter(delimiter);

        lineTokenizer.setNames(
                Arrays.stream(headerLine.split(delimiter))
                        .map(String::trim)
                        .map(String::toUpperCase)
                        .map(header -> header.replace(' ', '_'))
                        .map(header -> HEADER_TO_FIELD_MAPPING.getOrDefault(header, header))
                        .toArray(String[]::new));
    }

    private void mapAddress(Address source, Address target) {
        if (source == null) return;

        // Copy only non-empty properties to avoid overwriting existing values with nulls
        if (!isEmpty(source.getStreetName()))
            target.setStreetName(source.getStreetName());
        if (!isEmpty(source.getStreetNumber()))
            target.setStreetNumber(source.getStreetNumber());
        if (!isEmpty(source.getPostalCode()))
            target.setPostalCode(source.getPostalCode());
        if (!isEmpty(source.getCity()))
            target.setCity(source.getCity());
        if (!isEmpty(source.getStateProvince()))
            target.setStateProvince(source.getStateProvince());
        if (!isEmpty(source.getCountry()))
            target.setCountry(source.getCountry());
    }

    private void mapContact(Contact source, Contact target) {
        if (source == null) return;

        // Copy only non-empty properties to avoid overwriting existing values with nulls
        if (!isEmpty(source.getName()))
            target.setName(source.getName());
        if (!isEmpty(source.getPhone()))
            target.setPhone(source.getPhone());
        if (!isEmpty(source.getEmail()))
            target.setEmail(source.getEmail());
    }

    Buyer processLine(Datastream datastream, LineWrapper<Buyer> lineWrapper) {
        Buyer line = lineWrapper.entity();

        log.debug("Processing line: {}", line.getCode());

        resolveCompany(datastream, line);
        var buyer = findOrCreateBuyer(line);
        updateBuyerAddress(buyer, line);
        updateBuyerContact(buyer, line);
        updateBuyerIdNumber(buyer, line);

        return buyer;
    }

    private void resolveCompany(Datastream datastream, Buyer line) {
        if (line.getCompany() == null)
            throw new NoSuchElementException("Company not present in the file");

        line.setCompany(companyRepository
                .findOneByOrgIdAndCodeIgnoreCase(datastream.getOrgId(), line.getCompany().getCode())
                .orElseThrow(() -> new NoSuchElementException("Company not found: " + line.getCompany().getCode())));
    }

    private Buyer findOrCreateBuyer(Buyer line) {
        return buyerRepository.findOneByCompanyAndCode(line.getCompany(), line.getCode())
                .orElseGet(() -> new Buyer()
                        .setCode(line.getCode())
                        .setCompany(line.getCompany())
                        .setNumber(""))
                .setName(line.getName())
                .setPaymentTerms(line.getPaymentTerms());
    }

    private void updateBuyerAddress(Buyer buyer, Buyer line) {
        if (buyer.getAddress() == null) buyer.setAddress(new Address());
        mapAddress(line.getAddress(), buyer.getAddress());
        normalizeCountryCode(buyer);
    }

    private void normalizeCountryCode(Buyer buyer) {
        String country = buyer.getAddress().getCountry();
        if (country == null) return;

        // Check if it's a 3-letter code and convert if needed
        if (country.length() == 3) {
            String iso2 = Utils.iso3ToIso2(country);
            if (iso2 != null) {
                country = iso2;
            }
        }
        buyer.getAddress().setCountry(country.toLowerCase());
    }

    private void updateBuyerContact(Buyer buyer, Buyer line) {
        if (buyer.getContact() == null) buyer.setContact(new Contact());
        mapContact(line.getContact(), buyer.getContact());
    }

    private void updateBuyerIdNumber(Buyer buyer, Buyer line) {
        if (!isEmpty(line.getNumber()))
            buyer.setNumber(line.getNumber().replaceAll("[^a-zA-Z0-9]", "").toUpperCase());
        if (!isEmpty(line.getNumberType()))
            buyer.setNumberType(line.getNumberType());
    }
}
