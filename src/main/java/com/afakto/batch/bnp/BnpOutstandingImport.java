package com.afakto.batch.bnp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.ItemWriteListener;
import org.springframework.batch.core.JobInterruptedException;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.UnexpectedJobExecutionException;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.data.builder.RepositoryItemWriterBuilder;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.batch.item.file.LineMapper;
import org.springframework.batch.item.file.builder.FlatFileItemReaderBuilder;
import org.springframework.batch.item.file.mapping.BeanWrapperFieldSetMapper;
import org.springframework.batch.item.file.mapping.PatternMatchingCompositeLineMapper;
import org.springframework.batch.item.file.transform.FixedLengthTokenizer;
import org.springframework.batch.item.file.transform.Range;
import org.springframework.batch.item.file.transform.RangeArrayPropertyEditor;
import org.springframework.batch.item.support.builder.ClassifierCompositeItemWriterBuilder;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.core.io.Resource;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.ObjectUtils;

import com.afakto.batch.common.BatchErrorHandlers;
import com.afakto.batch.common.LineWrapper;
import com.afakto.domain.BaseEntity;
import com.afakto.domain.Buyer;
import com.afakto.domain.BuyerFromFactor;
import com.afakto.domain.Contract;
import com.afakto.domain.Datastream;
import com.afakto.domain.DatastreamFailure;
import com.afakto.domain.InvoiceFromFactor;
import com.afakto.repository.BuyerFromFactorRepository;
import com.afakto.repository.BuyerRepository;
import com.afakto.repository.ContractRepository;
import com.afakto.repository.InvoiceFromFactorRepository;
import com.afakto.repository.InvoiceRepository;
import com.afakto.service.CommentService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class BnpOutstandingImport {
    private static final String UNKNOWN_BUYER_NUMBER = "9PRO00000";

    private final BuyerFromFactorRepository buyerFromFactorRepository;
    private final BuyerRepository buyerRepository;
    private final CommentService commentService;
    private final ContractRepository contractRepository;
    private final InvoiceFromFactorRepository invoiceFromFactorRepository;
    private final InvoiceRepository invoiceRepository;

    private final JobRepository jobRepository;
    private final PlatformTransactionManager transactionManager;

    @SuppressWarnings({ "unchecked", "rawtypes" })
    public Step createStep(Datastream datastream, Resource resource) {
        Set<UUID> importedIds = new HashSet<>();
        Set<Buyer> unknownBuyers = new HashSet<>();
        Map<String, BigDecimal> approvedAmounts = new HashMap<>();

        var contract = contractRepository
                .findOneByCompanyOrgIdAndContractNumber(datastream.getOrgId(), datastream.getPath())
                .orElseThrow();

        SimpleStepBuilder result = new StepBuilder("importOutstandingStep", jobRepository)
                .<LineWrapper<BaseEntity>, BaseEntity>chunk(25, transactionManager)
                .reader(new FlatFileItemReaderBuilder<LineWrapper<BaseEntity>>()
                        .beanMapperStrict(false)
                        .lineMapper(createLineMapper())
                        .linesToSkip(1)
                        .skippedLinesCallback(headerLine -> checkHeader(datastream, headerLine))
                        .name("outstandingReader")
                        .resource(resource)
                        .strict(false)
                        .build())
                .processor(
                        lineWrapper -> processLine(datastream, lineWrapper, unknownBuyers, contract, approvedAmounts))
                .writer(new ClassifierCompositeItemWriterBuilder()
                        .classifier(item -> switch (item) {
                            case BuyerFromFactor ignored -> new RepositoryItemWriterBuilder<BuyerFromFactor>()
                                    .repository(buyerFromFactorRepository).build();
                            case InvoiceFromFactor ignored -> new RepositoryItemWriterBuilder<InvoiceFromFactor>()
                                    .repository(invoiceFromFactorRepository).build();
                            default -> null;
                        })
                        .build())
                .faultTolerant()
                .skip(FlatFileParseException.class)
                .skip(IncorrectResultSizeDataAccessException.class)
                .skip(NoSuchElementException.class)
                .skipLimit(10000)
                .listener(BatchErrorHandlers.createReadErrorListener(datastream))
                .listener(BatchErrorHandlers.createProcessErrorListener(datastream))
                .listener(new ItemWriteListener<BaseEntity>() {
                    @Override
                    public void beforeWrite(Chunk<? extends BaseEntity> items) {
                        items.forEach(item -> {
                            // Count inserts/updates before writing to database
                            // This avoids double-counting during chunk replays
                            if (item.getId() == null)
                                datastream.incrementInserts();
                            else
                                datastream.incrementUpdates();
                        });
                    }

                    @Override
                    public void afterWrite(Chunk<? extends BaseEntity> items) {
                        items.forEach(item -> {
                            if (item instanceof BuyerFromFactor)
                                importedIds.add(item.getId());
                        });

                    }
                });
        result.listener(new StepExecutionListener() {
            @Override
            public ExitStatus afterStep(StepExecution stepExecution) {
                resetNonImported(contract, datastream, importedIds);
                resetUnknowns(contract, unknownBuyers);
                updateApprovedAmounts(contract, approvedAmounts);
                return null;
            }
        });
        return result.build();
    }

    /**
     * Process a line wrapper containing either a BuyerFromFactor or
     * InvoiceFromFactor.
     * Validates the line and processes it according to its type.
     */
    BaseEntity processLine(
            Datastream datastream,
            LineWrapper<BaseEntity> lineWrapper,
            Set<Buyer> unknownBuyers,
            Contract contract,
            Map<String, BigDecimal> approvedAmounts) {
        BaseEntity item = lineWrapper.entity();

        // Important, to have the proper approved amount - calculate decimals early for
        // all entities
        if (item instanceof BuyerFromFactor buyerFromFactor) {
            buyerFromFactor.calculateDecimals();
            return processBuyer(datastream, contract, buyerFromFactor, lineWrapper, unknownBuyers, approvedAmounts);
        }

        if (item instanceof InvoiceFromFactor invoiceFromFactor) {
            invoiceFromFactor.calculateDecimals();
            return processInvoice(unknownBuyers, contract, invoiceFromFactor);
        }

        return item;
    }

    /**
     * Update remaining org/company buyers, to have their factor data reset to 0
     */
    private void resetNonImported(Contract contract, Datastream datastream, Set<UUID> importedIds) {
        if (importedIds.isEmpty())
            return;

        log.info("Resetting all buyer from factor data for org {}, company {} and currency {}. {} buyers imported.",
                datastream.getOrgId(),
                contract.getCompany().getCode(),
                contract.getFinancialInformation().getCurrency(),
                importedIds.size());

        var resets = buyerRepository.resetAllBalancesByOrgIdAndCompanyCodeAndCurrency(
                datastream.getOrgId(),
                contract.getCompany().getCode(),
                contract.getFinancialInformation().getCurrency(),
                importedIds);

        datastream.setDeletes(resets);
    }

    /**
     * To follow which buyer are not known by the factor
     */
    private void resetUnknowns(Contract contract, Set<Buyer> unknownBuyers) {
        unknownBuyers.forEach(b -> commentService.notify(b, "[BNP Sync] Unknown buyer:%n%s".formatted(b.getName())));
        var resets = buyerRepository.updateBuyerFromFactorUnknownByCompanyAndBuyers(
                contract.getCompany(),
                unknownBuyers);

        log.info("Unknown buyers reset on {} items", resets);
    }

    /**
     * Updates approved amounts for buyers collected during chunk processing.
     */
    private void updateApprovedAmounts(Contract contract, Map<String, BigDecimal> approvedAmounts) {
        if (approvedAmounts.isEmpty())
            return;

        int updatedCount = 0;
        for (Map.Entry<String, BigDecimal> entry : approvedAmounts.entrySet()) {
            String buyerNumber = entry.getKey();
            BigDecimal approvedAmount = entry.getValue();
            int updates = buyerRepository.updateAmountApproved(
                    contract.getCompany(),
                    buyerNumber,
                    contract.getFinancialInformation().getCurrency(),
                    approvedAmount);
            updatedCount += updates;
            log.debug("Updated approved amount for {} buyers with number: {}, amount: {} {}",
                    updates,
                    buyerNumber,
                    approvedAmount,
                    contract.getFinancialInformation().getCurrency());
        }

        log.info("Updated approved amounts for {} buyers (total {} database records updated)",
                approvedAmounts.size(), updatedCount);
    }

    /**
     * Process the skipped line to map the header to the field name.
     *
     * @param headerLine the header line
     * @throws JobInterruptedException
     */
    private void checkHeader(Datastream datastream, String headerLine) {
        if (headerLine == null || !headerLine.contains("V2.0"))
            return;

        datastream.setError("V2.0 file detected. Skipping import.");

        throw new UnexpectedJobExecutionException("V2.0 file detected. Skipping step.");
    }

    /*
     * This is the more complex part, that tokenizes the lines and maps them to the
     * target objects, wrapped in LineWrapper for better error handling
     */
    private LineMapper<LineWrapper<BaseEntity>> createLineMapper() {
        PatternMatchingCompositeLineMapper<BaseEntity> delegateMapper = createDelegateLineMapper();

        return (line, lineNumber) -> {
            BaseEntity entity = delegateMapper.mapLine(line, lineNumber);
            return new LineWrapper<>(lineNumber, line, entity);
        };
    }

    /*
     * Creates the original line mapper that handles the complex tokenization
     */
    private PatternMatchingCompositeLineMapper<BaseEntity> createDelegateLineMapper() {
        var buyerGlobalRange = new RangeArrayPropertyEditor();
        buyerGlobalRange.setAsText("11-19,25,65,68,69,86,103,120,137-153");
        var buyerGlobalTokenizer = new FixedLengthTokenizer();
        buyerGlobalTokenizer.setColumns((Range[]) buyerGlobalRange.getValue());
        buyerGlobalTokenizer.setNames("number", "name", "currency", "decimals",
                "amountApproved",
                "amountOutstanding", "amountUnSecured", "amountUnFunded", "amountDraftReceived");
        buyerGlobalTokenizer.setStrict(false);

        var buyerRange = new RangeArrayPropertyEditor();
        buyerRange.setAsText("11-19,25,65,68,69,86,103,120,137,146,181,186,216,265");
        var buyerTokenizer = new FixedLengthTokenizer();
        buyerTokenizer.setColumns((Range[]) buyerRange.getValue());
        buyerTokenizer.setNames("number", "name", "currency", "decimals",
                "amountOutstanding", "amountUnSecured", "amountUnFunded", "amountDraftReceived",
                "factorCode", "code", "zipcode", "city");
        buyerTokenizer.setStrict(false);

        var invoiceRange = new RangeArrayPropertyEditor();
        invoiceRange.setAsText("3,12,47,82,90,98,101,102-118,136-152,153,170,178-194,195-211,212-228,229-245");
        var invoiceTokenizer = new FixedLengthTokenizer();
        invoiceTokenizer.setColumns((Range[]) invoiceRange.getValue());
        invoiceTokenizer.setNames("invoice.buyer.buyerFromFactor.factorCode", "invoice.buyer.code", "invoiceNumber",
                "date", "dueDate",
                "currency", "decimals",
                "amount", "balance", "amountDraftReceived", "draftDueDate",
                "amountFunded", "amountUnfunded", "amountSecured", "amountUnsecured");
        invoiceTokenizer.setStrict(false);

        var buyerMapper = new BeanWrapperFieldSetMapper<BaseEntity>();
        buyerMapper.setTargetType(BuyerFromFactor.class);
        buyerMapper.setStrict(false);
        var invoiceMapper = new BeanWrapperFieldSetMapper<BaseEntity>();
        invoiceMapper.setTargetType(InvoiceFromFactor.class);
        invoiceMapper.setStrict(false);

        DefaultConversionService conversionService = new DefaultConversionService();
        DefaultConversionService.addDefaultConverters(conversionService);
        conversionService.addConverter(new Converter<String, LocalDate>() {
            @Override
            public LocalDate convert(String text) {
                return ObjectUtils.isEmpty(text) ? null : LocalDate.parse(text, DateTimeFormatter.BASIC_ISO_DATE);
            }
        });
        // Remove leading 0, which appear "before" the - negative sign
        conversionService.addConverter(new Converter<String, BigDecimal>() {
            @Override
            public BigDecimal convert(String text) {
                return new BigDecimal(StringUtils.stripStart(text, "0"));
            }
        });
        buyerMapper.setConversionService(conversionService);
        invoiceMapper.setConversionService(conversionService);

        var lineMapper = new PatternMatchingCompositeLineMapper<BaseEntity>();
        lineMapper.setTokenizers(Map.of(
                "10*", buyerGlobalTokenizer,
                "20*", buyerTokenizer,
                "30*", invoiceTokenizer,
                "*", fieldSet -> {
                    return null;
                }));
        lineMapper.setFieldSetMappers(Map.of(
                "10*", buyerMapper,
                "20*", buyerMapper,
                "30*", invoiceMapper,
                "*", fieldSet -> {
                    return null;
                }));

        return lineMapper;
    }

    private Buyer findBuyerByCode(Contract contract, BuyerFromFactor buyerFromFactor) {
        return buyerRepository
                .findOneByCompanyAndCode(contract.getCompany(), buyerFromFactor.getCode())
                .orElseThrow(() -> new NoSuchElementException("Buyer not found: " + buyerFromFactor.getCode()
                        + " (company: " + contract.getCompany().getCode() + ")"));
    }

    BaseEntity processBuyer(
            Datastream datastream,
            Contract contract,
            BuyerFromFactor buyerFromFactor,
            LineWrapper<BaseEntity> lineWrapper,
            Set<Buyer> unknownBuyers,
            Map<String, BigDecimal> approvedAmounts) {
        log.debug("Buyer {} with number: {} and code: {}",
                buyerFromFactor.getName(),
                buyerFromFactor.getNumber(),
                buyerFromFactor.getCode());

        if (UNKNOWN_BUYER_NUMBER.equals(buyerFromFactor.getNumber())) {
            if (buyerFromFactor.getCode() != null) {
                var unknownBuyer = findBuyerByCode(contract, buyerFromFactor);
                if (unknownBuyer != null)
                    unknownBuyers.add(unknownBuyer);
            }

            datastream.getFailures().add(new DatastreamFailure()
                    .setDatastream(datastream)
                    .setLine(lineWrapper.lineNumber())
                    .setMessage(String.format("%s => %d buyers", buyerFromFactor.getName(), unknownBuyers.size())));
            return null;
        }

        if (buyerFromFactor.getCode() == null) {
            // This is for lines «10» => agrément
            // 10013206709PROAC3CU WAL-MART CANADA CORP
            // CAD20000000014000000000000000112584351000000000000000000000000003251272400000000000000000
            // 000126
            log.debug("... approved amount: {} {}", buyerFromFactor.getAmountApproved(), buyerFromFactor.getCurrency());
            approvedAmounts.put(buyerFromFactor.getNumber(), buyerFromFactor.getAmountApproved());
            return null;
        }

        // This is for lines «20» => buyer
        var buyer = findBuyerByCode(contract, buyerFromFactor);

        if (buyer.getBuyerFromFactor() != null)
            // To properly overwrite the existing entity
            buyerFromFactor
                    .setId(buyer.getBuyerFromFactor().getId())
                    .setVersion(buyer.getBuyerFromFactor().getVersion());

        return buyerFromFactor.setBuyer(buyer);
    }

    private BaseEntity processInvoice(
            Set<Buyer> unknownBuyers,
            Contract contract,
            InvoiceFromFactor invoiceFromFactor) {
        var invoice = invoiceRepository
                .findOneByBuyerCompanyAndBuyerCodeAndInvoiceNumberAndAmountSign(
                        contract.getCompany(),
                        invoiceFromFactor.getInvoice().getBuyer().getCode(),
                        invoiceFromFactor.getInvoiceNumber(),
                        invoiceFromFactor.getAmount())
                .or(() -> invoiceRepository.findOneByBuyerCompanyAndInvoiceNumberAndAmountSign(
                        contract.getCompany(),
                        invoiceFromFactor.getInvoiceNumber(),
                        invoiceFromFactor.getAmount()))
                .orElseThrow(
                        () -> new NoSuchElementException(
                                "Invoice not found: " + invoiceFromFactor.getInvoiceNumber()
                                        + " (company: " + contract.getCompany().getCode()
                                        + ", buyer: " + invoiceFromFactor.getInvoice().getBuyer().getCode() + ")"));
        log.debug("Invoice {} funded {} {}",
                invoiceFromFactor.getInvoiceNumber(),
                invoiceFromFactor.getAmountFunded(),
                invoiceFromFactor.getCurrency());

        if (ObjectUtils.isEmpty(invoiceFromFactor.getInvoice().getBuyer().getCode()))
            unknownBuyers.add(invoice.getBuyer());

        if (invoice.getInvoiceFromFactor() != null) {
            // To properly overwrite the existing entity
            invoiceFromFactor
                    .setId(invoice.getInvoiceFromFactor().getId())
                    .setVersion(invoice.getInvoiceFromFactor().getVersion());
        } else if (!invoice.isUnderFactor()) {
            log.debug("... sold previously, marking it as under factor");
            invoiceRepository.save(invoice.setUnderFactor(true));
        }

        return invoiceFromFactor
                .setInvoice(invoice)
                .setType(invoice.getType());
    }
}
