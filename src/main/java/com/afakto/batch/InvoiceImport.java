package com.afakto.batch;

import static java.util.Map.entry;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Set;
import java.util.UUID;

import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.ItemWriteListener;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.data.builder.RepositoryItemWriterBuilder;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.batch.item.file.LineMapper;
import org.springframework.batch.item.file.builder.FlatFileItemReaderBuilder;
import org.springframework.batch.item.file.mapping.BeanWrapperFieldSetMapper;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

import com.afakto.batch.common.BatchErrorHandlers;
import com.afakto.batch.common.LineWrapper;
import com.afakto.domain.Datastream;
import com.afakto.domain.DatastreamFailure;
import com.afakto.domain.Invoice;
import com.afakto.repository.BuyerRepository;
import com.afakto.repository.CompanyRepository;
import com.afakto.repository.InvoiceRepository;
import com.afakto.service.CommentService;
import com.afakto.service.validation.InvoiceValidationService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class InvoiceImport {
    private final BuyerRepository buyerRepository;
    private final CommentService commentService;
    private final CompanyRepository companyRepository;
    private final CustomConversionService customConversionService;
    private final InvoiceRepository invoiceRepository;
    private final InvoiceValidationService invoiceValidationService;

    private final JobRepository jobRepository;
    private final PlatformTransactionManager transactionManager;

    private DelimitedLineTokenizer lineTokenizer;

    private static final Map<String, String> HEADER_TO_FIELD_MAPPING = Map.ofEntries(
            entry("BUYER_CODE", "buyer.code"),
            entry("COMPANY_CODE", "buyer.company.code"),
            entry("DUE_DATE", "dueDate"),
            entry("INV_DATE", "date"),
            entry("INV_NUMBER", "invoiceNumber"),
            entry("INV_TYPE", "type"),
            entry("AMOUNT", "amount"),
            entry("BALANCE", "balance"),
            entry("CURRENCY", "currency"),
            entry("PAYMENT_METHOD", "paymentMethod"),
            entry("RECONCILIATION_JOURNAL", "reconciliationJournal"));

    public Step createStep(Datastream datastream, Resource resource) {
        Set<UUID> importedIds = new HashSet<>();

        return new StepBuilder("importInvoiceStep", jobRepository)
                .<LineWrapper<Invoice>, Invoice>chunk(25, transactionManager)
                .reader(new FlatFileItemReaderBuilder<LineWrapper<Invoice>>()
                        .beanMapperStrict(false)
                        .lineMapper(createLineMapper())
                        .linesToSkip(1)
                        .skippedLinesCallback(this::setupHeader)
                        .name("invoiceReader")
                        .resource(resource)
                        .strict(false)
                        .build())
                .processor(lineWrapper -> processLine(datastream, lineWrapper))
                .writer(new RepositoryItemWriterBuilder<Invoice>()
                        .repository(invoiceRepository)
                        .build())
                .faultTolerant()
                .skip(FlatFileParseException.class)
                .skip(NoSuchElementException.class)
                .skipLimit(1000)
                .listener(BatchErrorHandlers.createReadErrorListener(datastream))
                .listener(BatchErrorHandlers.createProcessErrorListener(datastream))
                .listener(new ItemWriteListener<Invoice>() {
                    @Override
                    public void beforeWrite(Chunk<? extends Invoice> items) {
                        items.forEach(item -> {
                            // Count inserts/updates before writing to database
                            // This avoids double-counting during chunk replays
                            if (item.getId() == null)
                                datastream.incrementInserts();
                            else
                                datastream.incrementUpdates();
                        });
                    }

                    @Override
                    public void afterWrite(Chunk<? extends Invoice> items) {
                        items.forEach(item -> importedIds.add(item.getId()));
                    }
                })
                .listener(new StepExecutionListener() {
                    @Override
                    public ExitStatus afterStep(StepExecution stepExecution) {
                        // So that existing invoices, not reimported, have their balance reset to 0
                        resetNonImported(datastream, importedIds);
                        return null;
                    }
                })
                .build();
    }

    /**
     * Update remaining org/company invoices, to have their balance at 0
     */
    private void resetNonImported(Datastream datastream, Set<UUID> importedIds) {
        var company = companyRepository.findOneByOrgIdAndCodeIgnoreCase(datastream.getOrgId(), datastream.getPath())
                .orElseThrow(() -> new NoSuchElementException("Company not found: " + datastream.getPath()));

        if (importedIds.isEmpty()) {
            log.info("No invoices imported for org {} and company {}", datastream.getOrgId(), datastream.getPath());
            commentService.notify(company, "[Company Sync] No invoice imported for " + datastream.getPath());
            return;
        }

        var updates = buyerRepository.updateAllCaches(company);
        log.info("Updated {} buyers' caches", updates);

        log.info("Resetting all balances for org {} and company {}",
                datastream.getOrgId(),
                datastream.getPath());

        var resets = invoiceRepository.resetAllBalancesByOrgIdAndCompanyCode(
                datastream.getOrgId(), datastream.getPath(), importedIds);

        datastream.setDeletes(resets);
    }

    private LineMapper<LineWrapper<Invoice>> createLineMapper() {
        lineTokenizer = new DelimitedLineTokenizer();
        lineTokenizer.setStrict(false);

        BeanWrapperFieldSetMapper<Invoice> fieldSetMapper = new BeanWrapperFieldSetMapper<>();
        fieldSetMapper.setConversionService(customConversionService.getConversionService());
        fieldSetMapper.setStrict(false);
        fieldSetMapper.setTargetType(Invoice.class);

        DefaultLineMapper<Invoice> defaultLineMapper = new DefaultLineMapper<>();
        defaultLineMapper.setFieldSetMapper(fieldSetMapper);
        defaultLineMapper.setLineTokenizer(lineTokenizer);

        // Simple wrapper using method reference
        return (line, lineNumber) -> new LineWrapper<>(lineNumber, line, defaultLineMapper.mapLine(line, lineNumber));
    }

    /**
     * Process the skipped line to map the header to the field name.
     *
     * @param headerLine the header line
     */
    private void setupHeader(String headerLine) {
        // Determine and set the delimiter
        String delimiter = headerLine.contains(";") ? ";" : ",";
        lineTokenizer.setDelimiter(delimiter);

        lineTokenizer.setNames(
                Arrays.stream(headerLine.split(delimiter))
                        .map(String::trim)
                        .map(String::toUpperCase)
                        .map(header -> header.replace(' ', '_'))
                        .map(header -> HEADER_TO_FIELD_MAPPING.getOrDefault(header, header))
                        .toArray(String[]::new));
    }

    Invoice processLine(Datastream datastream, LineWrapper<Invoice> lineWrapper) {
        Invoice line = lineWrapper.entity();

        var failure = validateInvoiceForImport(lineWrapper);
        if (failure != null) {
            datastream.getFailures().add(failure.setDatastream(datastream));
            return null;
        }

        // Look up the buyer in the database
        var buyer = buyerRepository.findOneByCompanyOrgIdAndCompanyCodeAndCode(
                datastream.getOrgId(),
                line.getCompany().getCode(),
                line.getBuyer().getCode())
                .orElse(null);

        if (buyer == null) {
            var buyerFailure = createFailure(lineWrapper,
                    "Buyer not found: " + line.getBuyer().getCode()
                            + " (company: " + line.getCompany().getCode() + ")");
            datastream.getFailures().add(buyerFailure.setDatastream(datastream));
            return null;
        }

        line.setBuyer(buyer);

        return invoiceRepository
                .findOneByBuyerAndTypeAndInvoiceNumber(line.getBuyer(), line.getType(), line.getInvoiceNumber())
                .orElseGet(() -> new Invoice()
                        .setBuyer(line.getBuyer())
                        .setInvoiceNumber(line.getInvoiceNumber())
                        .setType(line.getType()))
                .setAmount(line.getAmount())
                .setBalance(line.getBalance())
                .setCurrency(line.getCurrency())
                .setDate(line.getDate())
                .setDueDate(line.getDueDate());
    }

    /**
     * Validates invoice data for import scenarios.
     * Uses the centralized validation service and converts results to
     * DatastreamFailure.
     */
    private DatastreamFailure validateInvoiceForImport(LineWrapper<Invoice> lineWrapper) {
        Invoice invoice = lineWrapper.entity();

        // Validate buyer and company presence first
        String error = invoiceValidationService.validateBuyerAndCompany(invoice);
        if (error != null)
            return createFailure(lineWrapper, error);

        // Validate required fields
        error = invoiceValidationService.validateRequiredFields(invoice);
        if (error != null)
            return createFailure(lineWrapper, error);

        // Validate amounts and balances
        error = invoiceValidationService.validateAmountsAndBalances(invoice);
        if (error != null)
            return createFailure(lineWrapper, error);

        return null;
    }

    /**
     * Creates a DatastreamFailure from a LineWrapper and error message.
     */
    private DatastreamFailure createFailure(LineWrapper<Invoice> lineWrapper, String message) {
        return new DatastreamFailure()
                .setLine(lineWrapper.lineNumber())
                .setMessage(message)
                .setRaw(lineWrapper.rawInput());
    }
}
