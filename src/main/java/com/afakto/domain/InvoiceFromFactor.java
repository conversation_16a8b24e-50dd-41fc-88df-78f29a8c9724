package com.afakto.domain;

import java.math.BigDecimal;
import java.time.LocalDate;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import com.afakto.domain.enumeration.InvoiceType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.OneToOne;
import lombok.Getter;
import lombok.Setter;

@Audited
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Entity
@Getter
@Setter
public class InvoiceFromFactor extends BaseEntity {
    @OneToOne(optional = false)
    private Invoice invoice;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private InvoiceType type;
    @Column(nullable = false)
    private String invoiceNumber;

    @Column(name = "invoice_date", nullable = false)
    private LocalDate date;
    @Column(nullable = false)
    private LocalDate dueDate;

    @Column(nullable = false)
    private String currency;
    @Column(nullable = false)
    private BigDecimal amount;
    @Column(nullable = false)
    private BigDecimal balance;
    @Column(nullable = false)
    private BigDecimal amountDraftReceived;
    private LocalDate draftDueDate;
    @Column(nullable = false)
    private BigDecimal amountFunded;
    @Column(nullable = false)
    private BigDecimal amountUnfunded;
    @Column(nullable = false)
    private BigDecimal amountSecured;
    @Column(nullable = false)
    private BigDecimal amountUnsecured;
    @Formula("amount_Secured + amount_Unsecured")
    @NotAudited
    private BigDecimal amountUnavailable;

    private transient int decimals;

    public InvoiceFromFactor calculateDecimals() {
        amount = amount.movePointLeft(decimals);
        balance = balance.movePointLeft(decimals);
        amountDraftReceived = amountDraftReceived.movePointLeft(decimals);
        amountFunded = amountFunded.movePointLeft(decimals);
        amountUnfunded = amountUnfunded.movePointLeft(decimals);
        amountSecured = amountSecured.movePointLeft(decimals);
        amountUnsecured = amountUnsecured.movePointLeft(decimals);

        decimals = 0;

        return this;
    }
}
