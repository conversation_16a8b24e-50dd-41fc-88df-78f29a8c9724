
package com.afakto.service.dto;

import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BuyerFromFactorDTO extends BaseDTO {

    private String number;
    private String name;
    private String code;
    private String factorCode;
    private String currency;

    private BigDecimal amountApproved;

    private BigDecimal amountOutstanding;
    private BigDecimal amountDraftReceived;
    private BigDecimal amountFunded;
    private BigDecimal amountSecured;

    private String zipcode;
    private String city;
}
