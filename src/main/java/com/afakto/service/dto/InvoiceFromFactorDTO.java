package com.afakto.service.dto;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.afakto.domain.enumeration.InvoiceType;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class InvoiceFromFactorDTO extends BaseDTO {

    @Enumerated(EnumType.STRING)
    private InvoiceType type;
    private LocalDate date;
    private LocalDate dueDate;
    private String invoiceNumber;
    private String currency;
    private BigDecimal amount;
    private BigDecimal balance;
    private BigDecimal amountDraftReceived;
    private LocalDate draftDueDate;
    private BigDecimal amountFunded;
    private BigDecimal amountUnfunded;
    private BigDecimal amountUnavailable;
}
