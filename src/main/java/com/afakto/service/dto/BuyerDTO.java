package com.afakto.service.dto;

import java.math.BigDecimal;

import com.afakto.domain.enumeration.NumberType;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * A DTO for the {@link com.afakto.domain.Buyer} entity.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuyerDTO extends BaseDTO {

    private CompanyDTO company;

    // Required to manage the @OneToOne non owning relation
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private BuyerFromFactorDTO buyerFromFactor;
    // Required to manage the @OneToOne non owning relation
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private BuyerFromInsurerDTO buyerFromInsurer;

    @NotNull
    private String code;
    @NotNull
    private NumberType numberType;
    @NotNull
    private String number;
    @NotNull
    private String name;

    private boolean excluded;
    private String exclusionReason;

    private AddressDTO address;
    private ContactDTO contact;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String currency;
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private BigDecimal balance;
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private CreditLimitDTO creditLimit;
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private boolean buyerFromFactorUnknown;
}
