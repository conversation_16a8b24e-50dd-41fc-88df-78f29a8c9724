package com.afakto.service;

import java.util.UUID;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import com.afakto.repository.BuyerRepository;
import com.afakto.repository.InvoiceRepository;
import com.afakto.service.dto.ContractDTO;
import com.afakto.service.dto.InvoiceDTO;
import com.afakto.service.mapper.BuyerMapper;
import com.afakto.service.mapper.CompanyMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class BuyerCacheAspect {

    private final BuyerMapper buyerMapper;
    private final BuyerRepository buyerRepository;
    private final CompanyMapper companyMapper;
    private final InvoiceRepository invoiceRepository;

    /**
     * After a contract is updated, update covers for all related buyers.
     */
    @AfterReturning(pointcut = "execution(* com.afakto.service.ContractService.save(..)) || " +
            "execution(* com.afakto.service.ContractService.update(..))", returning = "result")
    public void afterContractUpdate(JoinPoint joinPoint, ContractDTO result) {
        log.debug("Updating caches for all buyers after contract update: {}", result.getId());
        var updates = buyerRepository.updateAllCaches(companyMapper.toEntity(result.getCompany()));
        log.info("Updated {} buyers' caches", updates);
    }

    /**
     * After an invoice is saved or updated, update the related buyer's cache.
     */
    @AfterReturning(pointcut = "execution(* com.afakto.service.InvoiceService.save(..)) || " +
            "execution(* com.afakto.service.InvoiceService.update(..))", returning = "result")
    public void afterInvoiceChange(JoinPoint joinPoint, InvoiceDTO result) {
        log.debug("Updating cache for buyer after invoice change: {}", result.getBuyer().getId());
        buyerRepository.updateCache(buyerMapper.toEntity(result.getBuyer()));
    }

    /**
     * After an invoice is deleted, update the related buyer's cache.
     */
    @Around("execution(* com.afakto.service.InvoiceService.delete(java.util.UUID)) && args(invoiceId)")
    public void aroundInvoiceDelete(ProceedingJoinPoint joinPoint, UUID invoiceId) throws Throwable {
        var invoice = invoiceRepository.findById(invoiceId).orElseThrow();
        joinPoint.proceed();
        buyerRepository.updateCache(invoice.getBuyer());
    }
}
