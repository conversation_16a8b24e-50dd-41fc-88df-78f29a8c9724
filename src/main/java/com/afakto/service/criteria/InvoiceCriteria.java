package com.afakto.service.criteria;

import java.io.Serializable;

import org.springdoc.core.annotations.ParameterObject;

import com.afakto.domain.enumeration.GapType;
import com.afakto.domain.enumeration.InvoiceType;
import com.afakto.domain.enumeration.PaymentMethod;

import lombok.Data;
import tech.jhipster.service.Criteria;
import tech.jhipster.service.filter.BigDecimalFilter;
import tech.jhipster.service.filter.BooleanFilter;
import tech.jhipster.service.filter.Filter;
import tech.jhipster.service.filter.LocalDateFilter;
import tech.jhipster.service.filter.StringFilter;
import tech.jhipster.service.filter.UUIDFilter;

@Data
@ParameterObject
public class InvoiceCriteria implements Serializable, Criteria {
    private static final long serialVersionUID = 1L;

    private Boolean distinct;

    private UUIDFilter company;
    private UUIDFilter buyer;

    private Filter<InvoiceType> type;
    private StringFilter invoiceNumber;
    private LocalDateFilter date;
    private LocalDateFilter dueDate;
    private BooleanFilter buyerExcluded;
    private BooleanFilter paidOrExcluded;
    private LocalDateFilter paidOrExcludedOrDueDate;
    // Useful to manage the complexity of cases
    private UUIDFilter futureGapForContract;
    private StringFilter currency;
    private BigDecimalFilter amount;
    private BigDecimalFilter balance;
    private LocalDateFilter paymentDate;
    private Filter<PaymentMethod> paymentMethod;

    private BooleanFilter hasCover;
    private BooleanFilter isUnderFactor;

    private BigDecimalFilter invoiceFromFactor_amountFunded;
    private BigDecimalFilter invoiceFromFactor_amountUnfunded;
    private BigDecimalFilter invoiceFromFactor_amountUnavailable;

    private BigDecimalFilter buyerFromFactor_amountApproved;

    private UUIDFilter cession;

    private StringFilter reconciliationJournal;
    private BooleanFilter gap;
    private Filter<GapType> gapType;
    private BooleanFilter gapCredit;
    private UUIDFilter gapCession;

    public InvoiceCriteria() {
    }

    public InvoiceCriteria(InvoiceCriteria other) {
        this.distinct = other.distinct;

        this.company = other.company != null ? other.company.copy() : null;
        this.buyer = other.buyer != null ? other.buyer.copy() : null;
        this.type = other.type != null ? other.type.copy() : null;
        this.invoiceNumber = other.invoiceNumber != null ? other.invoiceNumber.copy() : null;
        this.date = other.date != null ? other.date.copy() : null;
        this.dueDate = other.dueDate != null ? other.dueDate.copy() : null;
        this.buyerExcluded = other.buyerExcluded != null ? other.buyerExcluded.copy() : null;
        this.paidOrExcluded = other.paidOrExcluded != null ? other.paidOrExcluded.copy() : null;
        this.paidOrExcludedOrDueDate = other.paidOrExcludedOrDueDate != null ? other.paidOrExcludedOrDueDate.copy()
                : null;
        this.futureGapForContract = other.futureGapForContract != null ? other.futureGapForContract.copy() : null;
        this.currency = other.currency != null ? other.currency.copy() : null;
        this.amount = other.amount != null ? other.amount.copy() : null;
        this.balance = other.balance != null ? other.balance.copy() : null;
        this.paymentDate = other.paymentDate != null ? other.paymentDate.copy() : null;
        this.paymentMethod = other.paymentMethod != null ? other.paymentMethod.copy() : null;
        this.hasCover = other.hasCover != null ? other.hasCover.copy() : null;
        this.isUnderFactor = other.isUnderFactor != null ? other.isUnderFactor.copy() : null;

        this.invoiceFromFactor_amountFunded = other.invoiceFromFactor_amountFunded != null
                ? other.invoiceFromFactor_amountFunded.copy()
                : null;
        this.invoiceFromFactor_amountUnfunded = other.invoiceFromFactor_amountUnfunded != null
                ? other.invoiceFromFactor_amountUnfunded.copy()
                : null;
        this.invoiceFromFactor_amountUnavailable = other.invoiceFromFactor_amountUnavailable != null
                ? other.invoiceFromFactor_amountUnavailable.copy()
                : null;

        this.buyerFromFactor_amountApproved = other.buyerFromFactor_amountApproved != null
                ? other.buyerFromFactor_amountApproved.copy()
                : null;
        this.cession = other.cession != null ? other.cession.copy() : null;

        this.reconciliationJournal = other.reconciliationJournal != null ? other.reconciliationJournal.copy() : null;
        this.gap = other.gap != null ? other.gap.copy() : null;
        this.gapType = other.gapType != null ? other.gapType.copy() : null;
        this.gapCredit = other.gapCredit != null ? other.gapCredit.copy() : null;
        this.gapCession = other.gapCession != null ? other.gapCession.copy() : null;
    }

    @Override
    public InvoiceCriteria copy() {
        return new InvoiceCriteria(this);
    }
}
